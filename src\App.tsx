import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { useEffect } from "react";
import { Analytics } from "@vercel/analytics/react";
import { useAuthStore } from "./stores/authStore";
import { useThemeStore } from "./stores/themeStore";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import { ProfileSimple } from "./pages/ProfileSimple";
import { AuthDebugSimple } from "./pages/AuthDebugSimple";
import { AuthDebugQuick } from "./pages/AuthDebugQuick";
import HiddenSpiritTest from "./components/HiddenSpiritTest";
import { PaymentSuccess } from "./pages/PaymentSuccess";
import { Pricing } from "./pages/Pricing";
import DesignSystemPage from "./pages/DesignSystemPage";

const queryClient = new QueryClient();

// Auth initialization component
const AuthInitializer = ({ children }: { children: React.ReactNode }) => {
  const initialize = useAuthStore(state => state.initialize);
  const { currentTheme, setTheme } = useThemeStore();

  useEffect(() => {
    initialize();
    // 确保主题在应用启动时正确应用
    setTheme(currentTheme);
  }, [initialize, currentTheme, setTheme]);

  return <>{children}</>;
};

const App = () => {
  console.log('App component rendering...')
  return (
  <QueryClientProvider client={queryClient}>
    <AuthInitializer>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/pricing" element={<Pricing />} />
            <Route path="/profile" element={<ProfileSimple />} />
            <Route path="/design-system" element={<DesignSystemPage />} />
            <Route path="/auth-debug" element={<AuthDebugSimple />} />
            <Route path="/auth-debug-quick" element={<AuthDebugQuick />} />
            <Route path="/hidden-spirit" element={<HiddenSpiritTest />} />
            <Route path="/payment-success" element={<PaymentSuccess />} />
            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
        <Analytics />
      </TooltipProvider>
    </AuthInitializer>
  </QueryClientProvider>
  )
}

export default App;
