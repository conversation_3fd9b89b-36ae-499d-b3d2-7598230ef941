import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export type ThemeType = 'modern' | 'cute';

export interface ThemeColors {
  background: string;
  foreground: string;
  card: string;
  cardForeground: string;
  primary: string;
  primaryForeground: string;
  secondary: string;
  secondaryForeground: string;
  muted: string;
  mutedForeground: string;
  accent: string;
  accentForeground: string;
  border: string;
  divinationPrimary: string;
  divinationSecondary: string;
  divinationAccent: string;
  gradientPrimary: string;
  gradientSecondary: string;
  gradientBackground: string;
}

export const themes: Record<ThemeType, ThemeColors> = {
  modern: {
    background: '240 10% 4%',
    foreground: '210 40% 95%',
    card: '240 8% 8%',
    cardForeground: '210 40% 95%',
    primary: '230 40% 20%',
    primaryForeground: '210 40% 98%',
    secondary: '45 90% 55%',
    secondaryForeground: '230 40% 15%',
    muted: '240 8% 15%',
    mutedForeground: '215 20% 65%',
    accent: '270 50% 25%',
    accentForeground: '210 40% 98%',
    border: '240 15% 18%',
    divinationPrimary: '230 60% 35%',
    divinationSecondary: '45 95% 60%',
    divinationAccent: '270 55% 30%',
    gradientPrimary: 'linear-gradient(135deg, hsl(230, 60%, 35%), hsl(270, 55%, 30%))',
    gradientSecondary: 'linear-gradient(135deg, hsl(45, 95%, 60%), hsl(38, 92%, 50%))',
    gradientBackground: 'linear-gradient(180deg, hsl(240, 8%, 8%), hsl(240, 10%, 4%))',
  },
  cute: {
    background: '330 100% 98%',
    foreground: '340 60% 35%',
    card: '340 70% 95%',
    cardForeground: '340 60% 25%',
    primary: '340 80% 60%',
    primaryForeground: '0 0% 100%',
    secondary: '310 70% 80%',
    secondaryForeground: '340 60% 25%',
    muted: '330 40% 92%',
    mutedForeground: '340 40% 55%',
    accent: '280 70% 75%',
    accentForeground: '340 60% 25%',
    border: '340 50% 85%',
    divinationPrimary: '340 80% 60%',
    divinationSecondary: '310 70% 80%',
    divinationAccent: '280 70% 75%',
    gradientPrimary: 'linear-gradient(135deg, hsl(340, 80%, 60%), hsl(310, 70%, 80%))',
    gradientSecondary: 'linear-gradient(135deg, hsl(280, 70%, 75%), hsl(45, 85%, 70%))',
    gradientBackground: 'linear-gradient(180deg, hsl(340, 70%, 95%), hsl(330, 100%, 98%))',
  },

};

interface ThemeState {
  currentTheme: ThemeType;
  setTheme: (theme: ThemeType) => void;
}

export const useThemeStore = create<ThemeState>()(
  persist(
    (set) => ({
      currentTheme: 'modern',
      setTheme: (theme: ThemeType) => {
        set({ currentTheme: theme });
        // 应用主题到CSS变量
        applyTheme(theme);
      },
    }),
    {
      name: 'theme-storage',
      onRehydrateStorage: () => (state) => {
        if (state?.currentTheme) {
          // 如果用户之前选择了已删除的古典主题，自动切换到现代主题
          const validTheme = (state.currentTheme as any) === 'classic' ? 'modern' : state.currentTheme;
          if (validTheme !== state.currentTheme) {
            set({ currentTheme: validTheme });
          }
          applyTheme(validTheme);
        }
      },
    }
  )
);

export const applyTheme = (theme: ThemeType) => {
  const colors = themes[theme];
  const root = document.documentElement;
  
  Object.entries(colors).forEach(([key, value]) => {
    const cssVar = key.replace(/([A-Z])/g, '-$1').toLowerCase();
    root.style.setProperty(`--${cssVar}`, value);
  });
  
  // 设置主题类名
  root.className = root.className.replace(/theme-\w+/g, '');
  root.classList.add(`theme-${theme}`);
};

export const themeNames: Record<ThemeType, string> = {
  modern: '现代科技风',
  cute: '可爱暖色风',
};