import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { DateTimePicker } from '@/components/ui/date-time-picker';
import { YaoType } from './YaoLine';
import { useTranslation } from '@/hooks/useTranslation';

interface CoinResult {
  coins: number[];
  sum: number;
  lineType: YaoType;
}

interface ManualCoinInputProps {
  onComplete: (lines: YaoType[], coinResults: CoinResult[], customDate?: Date) => void;
  onCancel: () => void;
}

export const ManualCoinInput: React.FC<ManualCoinInputProps> = ({ onComplete, onCancel }) => {
  const { t } = useTranslation();
  const [currentYao, setCurrentYao] = useState(1);
  const [coins, setCoins] = useState<number[]>([]);
  const [allLines, setAllLines] = useState<YaoType[]>([]);
  const [coinResults, setCoinResults] = useState<CoinResult[]>([]);
  const [useCustomDate, setUseCustomDate] = useState(false);
  const [customDate, setCustomDate] = useState<Date>(new Date());

  const handleCoinClick = (value: number) => {
    const newCoins = [...coins, value];
    setCoins(newCoins);

    if (newCoins.length === 3) {
      const sum = newCoins.reduce((a, b) => a + b, 0);
      let lineType: YaoType;
      
      if (sum === 6) lineType = 'yin_changing';
      else if (sum === 9) lineType = 'yang_changing';
      else if (sum === 7) lineType = 'yang';
      else lineType = 'yin';

      const newAllLines = [...allLines, lineType];
      setAllLines(newAllLines);

      const newCoinResult: CoinResult = {
        coins: [...newCoins],
        sum,
        lineType
      };
      const newCoinResults = [...coinResults, newCoinResult];
      setCoinResults(newCoinResults);

      if (currentYao === 6) {
        onComplete(newAllLines, newCoinResults, useCustomDate ? customDate : undefined);
      } else {
        setCurrentYao(currentYao + 1);
        setCoins([]);
      }
    }
  };

  const getCoinDisplay = (value: number) => value === 3 ? t('coinWord') : t('coinBack');
  const coinTextCount = (coins: number[]) => coins.filter(c => c === 3).length;
  const coinBackCount = (coins: number[]) => coins.filter(c => c === 2).length;

  return (
    <div className="space-y-6">
      {/* 日期选择区域 */}
      <div className="space-y-4 p-4 bg-background/20 rounded-lg">
        <div className="flex items-center space-x-2">
          <Switch
            id="custom-date"
            checked={useCustomDate}
            onCheckedChange={setUseCustomDate}
          />
          <Label htmlFor="custom-date" className="text-sm font-medium">
            {t('manualDateSetting')}
          </Label>
        </div>

        {useCustomDate && (
          <div className="space-y-2">
            <Label className="text-sm text-muted-foreground">
              {t('selectDateTime')}
            </Label>
            <DateTimePicker
              value={customDate}
              onChange={setCustomDate}
              placeholder={t('selectDateTimePlaceholder')}
            />
          </div>
        )}

        {!useCustomDate && (
          <p className="text-sm text-muted-foreground">
            {t('useCurrentTime')}
          </p>
        )}
      </div>

      <div className="text-center">
        <h3 className="text-xl font-bold mb-2">{t('yaoLineNumber').replace('{number}', currentYao.toString())}</h3>
        <p className="text-muted-foreground">{t('clickCoinResults')}</p>
      </div>

      <div className="flex justify-center gap-4">
        {[1, 2, 3].map((coinIndex) => (
          <div key={coinIndex} className="text-center">
            <div className="text-sm text-muted-foreground mb-2">{t('coinNumber').replace('{number}', coinIndex.toString())}</div>
            <div className="space-y-2">
              <Button
                variant={coins[coinIndex - 1] === 3 ? "default" : "outline"}
                size="lg"
                onClick={() => handleCoinClick(3)}
                disabled={coins.length >= coinIndex}
                className="w-16 h-16 text-lg"
              >
                {t('coinWord')}
              </Button>
              <Button
                variant={coins[coinIndex - 1] === 2 ? "default" : "outline"}
                size="lg"
                onClick={() => handleCoinClick(2)}
                disabled={coins.length >= coinIndex}
                className="w-16 h-16 text-lg"
              >
                {t('coinBack')}
              </Button>
            </div>
          </div>
        ))}
      </div>

      {coins.length > 0 && (
        <div className="text-center">
          <p className="text-sm text-muted-foreground">
            {t('selectedCoins').replace('{coins}', coins.map(getCoinDisplay).join(' + '))}
            {coins.length === 3 && (
              <span className="ml-2 text-divination-primary">
                = {coins.reduce((a, b) => a + b, 0)}
                ({coins.reduce((a, b) => a + b, 0) === 6 ? t('oldYin') :
                  coins.reduce((a, b) => a + b, 0) === 9 ? t('oldYang') :
                  coins.reduce((a, b) => a + b, 0) === 7 ? t('youngYang') : t('youngYin')})
              </span>
            )}
          </p>
        </div>
      )}

      {allLines.length > 0 && (
        <div className="text-center">
          <p className="text-sm text-muted-foreground">
            {t('completedLines').replace('{count}', allLines.length.toString())}
          </p>
        </div>
      )}

      {/* 摇卦历史 */}
      {coinResults.length > 0 && (
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-center">{t('tossRecord')}</h4>
          <div className="grid grid-cols-6 gap-2 text-center">
            {coinResults.map((result, index) => (
              <div key={index} className="space-y-1 p-2 bg-background/20 rounded text-xs">
                <div className="text-muted-foreground">{t('lineNumber').replace('{number}', (index + 1).toString())}</div>
                <div className="text-divination-primary">
                  {coinTextCount(result.coins)}{t('coinWord')}{coinBackCount(result.coins)}{t('coinBack')}
                </div>
                <div className="text-xs">
                  {result.sum === 6 ? t('oldYin') :
                   result.sum === 9 ? t('oldYang') :
                   result.sum === 7 ? t('youngYang') : t('youngYin')}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="flex gap-4">
        <Button variant="outline" onClick={onCancel} className="flex-1">
          {t('cancel')}
        </Button>
      </div>
    </div>
  );
};