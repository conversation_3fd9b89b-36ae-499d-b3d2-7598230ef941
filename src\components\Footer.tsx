import React from 'react';
import { Separator } from '@/components/ui/separator';
import { useTranslation } from '@/hooks/useTranslation';
import { useThemeStore } from '@/stores/themeStore';
import { cn } from '@/lib/utils';
import { Heart, Star, Sparkles, Zap, Shield, Database } from 'lucide-react';

export const Footer: React.FC = () => {
  const { t } = useTranslation();
  const { currentTheme } = useThemeStore();

  // Footer样式函数
  const getFooterStyles = () => {
    const baseStyles = "mt-20 border-t backdrop-blur-sm relative overflow-hidden";

    switch (currentTheme) {
      case 'modern':
        return cn(
          baseStyles,
          "border-slate-800/50 bg-slate-900/80",
          "shadow-lg shadow-blue-500/5"
        );
      case 'cute':
        return cn(
          baseStyles,
          "border-pink-200/50 bg-pink-50/80",
          "shadow-lg shadow-pink-300/10"
        );
      default:
        return cn(baseStyles, "border-border/50 bg-card/20");
    }
  };

  const getContainerStyles = () => {
    const baseStyles = "max-w-7xl mx-auto relative z-10";

    switch (currentTheme) {
      case 'modern':
        return cn(baseStyles, "px-6 py-16 md:py-20");
      case 'cute':
        return cn(baseStyles, "px-6 py-14 md:py-18");
      default:
        return cn(baseStyles, "px-6 py-12");
    }
  };

  const getGridStyles = () => {
    switch (currentTheme) {
      case 'modern':
        return "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12";
      case 'cute':
        return "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-10";
      default:
        return "grid md:grid-cols-4 gap-8";
    }
  };

  const getBrandStyles = () => {
    switch (currentTheme) {
      case 'modern':
        return "space-y-6";
      case 'cute':
        return "space-y-5";
      default:
        return "space-y-4";
    }
  };

  const getBrandTitleStyles = () => {
    switch (currentTheme) {
      case 'modern':
        return "font-bold text-xl bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent";
      case 'cute':
        return "font-bold text-lg gradient-text flex items-center gap-2";
      default:
        return "font-bold text-lg gradient-text";
    }
  };

  const getBrandDescStyles = () => {
    switch (currentTheme) {
      case 'modern':
        return "text-sm leading-relaxed text-slate-300 max-w-xs";
      case 'cute':
        return "text-sm leading-relaxed text-pink-700 font-medium max-w-xs";
      default:
        return "text-sm text-muted-foreground leading-relaxed";
    }
  };

  const getSectionStyles = () => {
    switch (currentTheme) {
      case 'modern':
        return "space-y-6";
      case 'cute':
        return "space-y-5";
      default:
        return "space-y-4";
    }
  };

  const getSectionTitleStyles = () => {
    switch (currentTheme) {
      case 'modern':
        return "font-semibold text-base text-blue-300";
      case 'cute':
        return "font-semibold text-base text-pink-600";
      default:
        return "font-semibold";
    }
  };

  const getListStyles = () => {
    switch (currentTheme) {
      case 'modern':
        return "space-y-3 text-sm";
      case 'cute':
        return "space-y-3 text-sm";
      default:
        return "space-y-2 text-sm text-muted-foreground";
    }
  };

  const getListItemStyles = () => {
    switch (currentTheme) {
      case 'modern':
        return "text-slate-400 hover:text-blue-300 transition-colors duration-200 cursor-pointer flex items-center gap-2";
      case 'cute':
        return "text-pink-600 hover:text-pink-500 transition-colors duration-200 cursor-pointer flex items-center gap-2";
      default:
        return "text-muted-foreground";
    }
  };

  // 背景效果组件
  const FooterBackgroundEffects = () => {
    if (currentTheme === 'modern') {
      return (
        <div className="absolute inset-0 overflow-hidden opacity-20">
          {[...Array(15)].map((_, i) => (
            <div
              key={`footer-star-${i}`}
              className="absolute bg-blue-200 rounded-full opacity-30"
              style={{
                width: `${0.5 + Math.random() * 1}px`,
                height: `${0.5 + Math.random() * 1}px`,
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animation: `twinkle ${3 + Math.random() * 2}s ease-in-out infinite`,
                animationDelay: `${Math.random() * 3}s`
              }}
            />
          ))}
        </div>
      );
    }

    if (currentTheme === 'cute') {
      return (
        <div className="absolute inset-0 overflow-hidden pointer-events-none opacity-15">
          {[...Array(8)].map((_, i) => (
            <div
              key={`footer-cute-${i}`}
              className="absolute"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animation: `float ${8 + Math.random() * 4}s ease-in-out infinite`,
                animationDelay: `${Math.random() * 6}s`,
              }}
            >
              {i % 2 === 0 ? (
                <Heart className="w-4 h-4 text-pink-300" />
              ) : (
                <Star className="w-3 h-3 text-orange-300" />
              )}
            </div>
          ))}
        </div>
      );
    }

    return null;
  };

  return (
    <footer className={getFooterStyles()}>
      <FooterBackgroundEffects />
      <div className={getContainerStyles()}>
        <div className={getGridStyles()}>
          {/* 品牌区域 */}
          <div className={getBrandStyles()}>
            <h3 className={getBrandTitleStyles()}>
              {currentTheme === 'cute' && <Heart className="w-5 h-5 text-pink-500" />}
              {t('appName')}
              {currentTheme === 'modern' && <Sparkles className="w-4 h-4 text-blue-400 ml-2 inline" />}
            </h3>
            <p className={getBrandDescStyles()}>
              {t('tagline')}
            </p>
            {currentTheme === 'modern' && (
              <div className="flex items-center gap-2 text-xs text-slate-400">
                <Zap className="w-3 h-3" />
                <span>AI驱动的智能占卜平台</span>
              </div>
            )}
            {currentTheme === 'cute' && (
              <div className="flex items-center gap-2 text-xs text-pink-500">
                <Star className="w-3 h-3" />
                <span>温馨陪伴每一次求问</span>
              </div>
            )}
          </div>

          {/* 核心功能区域 */}
          <div className={getSectionStyles()}>
            <h4 className={getSectionTitleStyles()}>{t('coreFeatures')}</h4>
            <ul className={getListStyles()}>
              <li className={getListItemStyles()}>
                {currentTheme === 'modern' ? <Database className="w-3 h-3" /> : <Heart className="w-3 h-3" />}
                {t('hexagramPlanning')}
              </li>
              <li className={getListItemStyles()}>
                {currentTheme === 'modern' ? <Zap className="w-3 h-3" /> : <Sparkles className="w-3 h-3" />}
                {t('aiIntelligentAnalysis')}
              </li>
              <li className={getListItemStyles()}>
                {currentTheme === 'modern' ? <Shield className="w-3 h-3" /> : <Star className="w-3 h-3" />}
                {t('historyRecords')}
              </li>
              <li className={getListItemStyles()}>
                {currentTheme === 'modern' ? <Database className="w-3 h-3" /> : <Heart className="w-3 h-3" />}
                {t('shareCollection')}
              </li>
            </ul>
          </div>

          {/* 学习资源区域 */}
          <div className={getSectionStyles()}>
            <h4 className={getSectionTitleStyles()}>{t('learningResources')}</h4>
            <ul className={getListStyles()}>
              <li className={getListItemStyles()}>
                {currentTheme === 'modern' ? <Database className="w-3 h-3" /> : <Star className="w-3 h-3" />}
                {t('hexagramBasics')}
              </li>
              <li className={getListItemStyles()}>
                {currentTheme === 'modern' ? <Zap className="w-3 h-3" /> : <Sparkles className="w-3 h-3" />}
                {t('hexagramInterpretation')}
              </li>
              <li className={getListItemStyles()}>
                {currentTheme === 'modern' ? <Shield className="w-3 h-3" /> : <Heart className="w-3 h-3" />}
                {t('caseAnalysis')}
              </li>
              <li className={getListItemStyles()}>
                {currentTheme === 'modern' ? <Database className="w-3 h-3" /> : <Star className="w-3 h-3" />}
                {t('frequentQuestions')}
              </li>
            </ul>
          </div>

          {/* 联系我们区域 */}
          <div className={getSectionStyles()}>
            <h4 className={getSectionTitleStyles()}>{t('contactUs')}</h4>
            <ul className={getListStyles()}>
              <li className={getListItemStyles()}>
                {currentTheme === 'modern' ? <Shield className="w-3 h-3" /> : <Heart className="w-3 h-3" />}
                {t('customerSupport')}
              </li>
              <li className={getListItemStyles()}>
                {currentTheme === 'modern' ? <Zap className="w-3 h-3" /> : <Sparkles className="w-3 h-3" />}
                {t('feedbackSuggestions')}
              </li>
              <li className={getListItemStyles()}>
                {currentTheme === 'modern' ? <Database className="w-3 h-3" /> : <Star className="w-3 h-3" />}
                {t('businessCooperation')}
              </li>
              <li className={getListItemStyles()}>
                {currentTheme === 'modern' ? <Shield className="w-3 h-3" /> : <Heart className="w-3 h-3" />}
                {t('privacyPolicy')}
              </li>
            </ul>
          </div>
        </div>

        {/* 分隔线 */}
        <Separator className={cn(
          "my-10 md:my-12",
          currentTheme === 'modern' ? "bg-slate-700/50" :
          currentTheme === 'cute' ? "bg-pink-200/50" : "bg-border/50"
        )} />

        {/* 底部版权区域 */}
        <div className={cn(
          "flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",
          currentTheme === 'modern' && "text-slate-400",
          currentTheme === 'cute' && "text-pink-600"
        )}>
          <div className="flex flex-col md:flex-row items-center gap-4 md:gap-6">
            <p className={cn(
              "text-sm",
              currentTheme === 'modern' ? "text-slate-400" :
              currentTheme === 'cute' ? "text-pink-600" : "text-muted-foreground"
            )}>
              {t('copyright')}
            </p>
            {currentTheme === 'modern' && (
              <div className="flex items-center gap-2 text-xs text-slate-500">
                <Zap className="w-3 h-3" />
                <span>Powered by AI Technology</span>
              </div>
            )}
            {currentTheme === 'cute' && (
              <div className="flex items-center gap-2 text-xs text-pink-500">
                <Heart className="w-3 h-3" />
                <span>用心制作，温暖陪伴</span>
              </div>
            )}
          </div>

          <div className="flex items-center gap-2">
            <p className={cn(
              "text-sm",
              currentTheme === 'modern' ? "text-slate-400" :
              currentTheme === 'cute' ? "text-pink-600" : "text-muted-foreground"
            )}>
              {t('tagline')}
            </p>
            {currentTheme === 'modern' && <Sparkles className="w-4 h-4 text-blue-400" />}
            {currentTheme === 'cute' && <Star className="w-4 h-4 text-pink-400" />}
          </div>
        </div>
      </div>
    </footer>
  );
};