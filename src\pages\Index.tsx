import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Header } from '@/components/Header';
import { Footer } from '@/components/Footer';
import { DivinationInterface } from '@/components/DivinationInterface';
import { DivinationCard } from '@/components/DivinationCard';

import { ThemeSwitcher } from '@/components/ThemeSwitcher';
import { SavedRecordsPanel } from '@/components/SavedRecordsPanel';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, Zap, Shield, Users, CheckCircle, XCircle, Sparkles, Heart, Scroll, BarChart3, Database, Cpu, Star, Sun, Flower } from 'lucide-react';
import { toast } from 'sonner';
import { useAuthStore } from '@/stores/authStore';
import { useThemeStore } from '@/stores/themeStore';
import { useTranslation } from '@/hooks/useTranslation';
import { supabase } from '@/lib/supabase';
import { ScrollReveal } from '@/components/ScrollReveal';
import modernBg from '@/assets/modern-tech-bg.jpg';
import cuteBg from '@/assets/cute-warm-bg.jpg';

import heroBackground from '@/assets/hero-background.jpg';

const Index = () => {
  console.log('Index page rendering...')
  const [searchParams, setSearchParams] = useSearchParams();
  const { user } = useAuthStore();
  const { currentTheme } = useThemeStore();
  const { t } = useTranslation();

  // 获取主题特色图标
  const getThemeIcon = () => {
    switch (currentTheme) {
      case 'modern': return Zap;
      case 'cute': return Heart;
      default: return Zap;
    }
  };

  const ThemeIcon = getThemeIcon();

  // 获取主题背景图
  const getThemeBackground = () => {
    switch (currentTheme) {
      case 'modern': return modernBg;
      case 'cute': return heroBackground;
      default: return modernBg;
    }
  };

  // 获取主题标题
  const getThemeTitle = () => {
    switch (currentTheme) {
      case 'modern': return t('modernTitle');
      case 'cute': return t('cuteTitle');
      default: return t('modernTitle');
    }
  };

  // 获取主题描述
  const getThemeDescription = () => {
    switch (currentTheme) {
      case 'modern': return t('modernDesc');
      case 'cute': return t('cuteDesc');
      default: return t('modernDesc');
    }
  };

  // Hero Section 样式函数
  const getHeroSectionStyles = () => {
    const baseStyles = "py-16 md:py-20 lg:py-24 px-6 relative flex items-center overflow-hidden";

    switch (currentTheme) {
      case 'modern':
        return cn(baseStyles, "min-h-[80vh]");
      case 'cute':
        return cn(baseStyles, "min-h-[80vh] hero-enhanced");
      default:
        return baseStyles;
    }
  };

  const getHeroBackgroundStyle = () => {
    switch (currentTheme) {
      case 'modern':
        return {
          background: `
            radial-gradient(ellipse at top, rgba(29, 78, 216, 0.15) 0%, transparent 50%),
            radial-gradient(ellipse at bottom left, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
            radial-gradient(ellipse at bottom right, rgba(219, 39, 119, 0.1) 0%, transparent 50%),
            radial-gradient(ellipse at center right, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
            linear-gradient(180deg, #0a0a0f 0%, #1a1a2e 30%, #16213e 70%, #0f172a 100%)
          `,
        };
      case 'cute':
        return {
          background: `
            radial-gradient(ellipse at top, rgba(255, 255, 255, 0.3) 0%, transparent 60%),
            radial-gradient(ellipse at bottom left, rgba(252, 182, 159, 0.4) 0%, transparent 50%),
            radial-gradient(ellipse at bottom right, rgba(255, 236, 210, 0.4) 0%, transparent 50%),
            linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)
          `,
          backgroundAttachment: 'fixed'
        };
      default:
        return {};
    }
  };

  const getMainTitleStyles = () => {
    switch (currentTheme) {
      case 'modern':
        return "text-6xl md:text-7xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-200 bg-clip-text text-transparent leading-tight";
      case 'cute':
        return "text-6xl md:text-7xl lg:text-8xl font-bold gradient-text leading-tight";
      default:
        return "text-6xl md:text-7xl font-bold";
    }
  };

  const getDescriptionStyles = () => {
    switch (currentTheme) {
      case 'modern':
        return "text-xl md:text-2xl text-gray-300 leading-relaxed max-w-4xl mx-auto";
      case 'cute':
        return "bg-white/85 backdrop-blur-md rounded-3xl p-10 md:p-12 shadow-2xl border-4 border-pink-200 max-w-4xl mx-auto";
      default:
        return "text-xl md:text-2xl leading-relaxed max-w-4xl mx-auto";
    }
  };

  const getCtaButtonStyles = () => {
    switch (currentTheme) {
      case 'modern':
        return "btn-primary text-lg px-12 py-6 ripple-effect hover-lift click-scale";
      case 'cute':
        return "btn-primary text-xl px-16 py-6 ripple-effect hover-lift click-scale";
      default:
        return "btn-primary text-lg px-8 py-4 ripple-effect hover-lift click-scale";
    }
  };

  // 背景效果组件
  const ModernBackgroundEffects = () => (
    <div className="absolute inset-0 overflow-hidden hardware-accelerated">
      {/* 大星星 */}
      {[...Array(20)].map((_, i) => (
        <div
          key={`big-star-${i}`}
          className="absolute bg-white rounded-full opacity-80 optimized-animation"
          style={{
            width: `${2 + Math.random() * 3}px`,
            height: `${2 + Math.random() * 3}px`,
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
            animation: `twinkle ${3 + Math.random() * 4}s ease-in-out infinite`,
            animationDelay: `${Math.random() * 5}s`,
            boxShadow: '0 0 6px rgba(255, 255, 255, 0.8)',
            willChange: 'opacity, transform'
          }}
        />
      ))}
      {/* 中等星星 */}
      {[...Array(40)].map((_, i) => (
        <div
          key={`med-star-${i}`}
          className="absolute bg-blue-100 rounded-full opacity-60"
          style={{
            width: `${1 + Math.random() * 2}px`,
            height: `${1 + Math.random() * 2}px`,
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
            animation: `twinkle ${2 + Math.random() * 3}s ease-in-out infinite`,
            animationDelay: `${Math.random() * 4}s`,
            boxShadow: '0 0 4px rgba(191, 219, 254, 0.6)'
          }}
        />
      ))}
      {/* 小星星 */}
      {[...Array(80)].map((_, i) => (
        <div
          key={`small-star-${i}`}
          className="absolute bg-purple-200 rounded-full opacity-40"
          style={{
            width: `${0.5 + Math.random() * 1}px`,
            height: `${0.5 + Math.random() * 1}px`,
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
            animation: `twinkle ${1.5 + Math.random() * 2}s ease-in-out infinite`,
            animationDelay: `${Math.random() * 3}s`
          }}
        />
      ))}
      {/* 流星效果 */}
      {[...Array(3)].map((_, i) => (
        <div
          key={`meteor-${i}`}
          className="absolute w-1 h-1 bg-white rounded-full opacity-0"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 50}%`,
            animation: `meteor ${8 + Math.random() * 4}s linear infinite`,
            animationDelay: `${Math.random() * 10}s`,
            boxShadow: '0 0 8px rgba(255, 255, 255, 0.8), 0 0 16px rgba(59, 130, 246, 0.4)'
          }}
        />
      ))}
    </div>
  );

  const CuteBackgroundEffects = () => (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {/* 浮动的心形和星星 */}
      {[...Array(15)].map((_, i) => (
        <div
          key={`floating-${i}`}
          className="absolute opacity-20"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
            animation: `float ${6 + Math.random() * 4}s ease-in-out infinite`,
            animationDelay: `${Math.random() * 5}s`,
          }}
        >
          {i % 3 === 0 ? (
            <Heart className="w-6 h-6 text-pink-300" />
          ) : i % 3 === 1 ? (
            <Star className="w-5 h-5 text-yellow-300" />
          ) : (
            <Flower className="w-4 h-4 text-orange-300" />
          )}
        </div>
      ))}
    </div>
  );

  // 主界面样式函数
  const getMainInterfaceStyles = () => {
    const baseStyles = "py-20 md:py-24 lg:py-28 px-6 relative";

    switch (currentTheme) {
      case 'modern':
        return cn(baseStyles, "overflow-hidden");
      case 'cute':
        return cn(baseStyles, "bg-gradient-to-br from-pink-50/50 to-orange-50/50");
      default:
        return baseStyles;
    }
  };

  const getMainInterfaceBackgroundStyle = () => {
    switch (currentTheme) {
      case 'modern':
        return {
          background: `
            radial-gradient(ellipse at top, rgba(29, 78, 216, 0.08) 0%, transparent 50%),
            radial-gradient(ellipse at bottom, rgba(139, 92, 246, 0.08) 0%, transparent 50%),
            linear-gradient(180deg, #0f172a 0%, #1e293b 50%, #0f172a 100%)
          `
        };
      case 'cute':
        return {
          background: `
            radial-gradient(ellipse at center, rgba(255, 182, 193, 0.1) 0%, transparent 60%),
            linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)
          `
        };
      default:
        return {};
    }
  };

  const getFeaturesSectionStyles = () => {
    const baseStyles = "py-24 md:py-32 lg:py-40 px-6 relative min-h-screen";

    switch (currentTheme) {
      case 'modern':
        return cn(baseStyles, "overflow-hidden");
      case 'cute':
        return cn(baseStyles, "bg-gradient-to-br from-orange-50/30 to-pink-50/30");
      default:
        return baseStyles;
    }
  };

  const getFeaturesBackgroundStyle = () => {
    switch (currentTheme) {
      case 'modern':
        return {
          background: `
            radial-gradient(ellipse at center, rgba(29, 78, 216, 0.05) 0%, transparent 50%),
            linear-gradient(180deg, #1e293b 0%, #0f172a 50%, #1e293b 100%)
          `
        };
      case 'cute':
        return {
          background: `
            radial-gradient(ellipse at top, rgba(255, 182, 193, 0.08) 0%, transparent 50%),
            radial-gradient(ellipse at bottom, rgba(255, 236, 210, 0.08) 0%, transparent 50%)
          `
        };
      default:
        return {};
    }
  };

  const getFeaturesTitleStyles = () => {
    switch (currentTheme) {
      case 'modern':
        return "text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent";
      case 'cute':
        return "text-4xl md:text-5xl font-bold mb-6 gradient-text";
      default:
        return "text-4xl md:text-5xl font-bold mb-6 text-foreground";
    }
  };

  const getFeaturesDescriptionStyles = () => {
    switch (currentTheme) {
      case 'modern':
        return "text-xl leading-relaxed max-w-3xl mx-auto text-slate-300";
      case 'cute':
        return "text-xl leading-relaxed max-w-3xl mx-auto text-pink-700 font-medium";
      default:
        return "text-xl leading-relaxed max-w-3xl mx-auto text-muted-foreground";
    }
  };

  const getFeatureCardsGridStyles = () => {
    switch (currentTheme) {
      case 'modern':
        return "grid lg:grid-cols-3 md:grid-cols-2 grid-cols-1 gap-10 lg:gap-12 xl:gap-16";
      case 'cute':
        return "grid lg:grid-cols-3 md:grid-cols-2 grid-cols-1 gap-10 lg:gap-14 xl:gap-18";
      default:
        return "grid md:grid-cols-3 gap-10";
    }
  };

  const getFeaturesSectionTitle = () => {
    switch (currentTheme) {
      case 'modern': return t('intelligentDivinationSystem');
      case 'cute': return t('cuteDivinationFeatures');
      default: return t('traditionalFeatures');
    }
  };

  const getFeaturesSectionDescription = () => {
    switch (currentTheme) {
      case 'modern': return t('modernSystemDesc');
      case 'cute': return t('cuteSystemDesc');
      default: return t('traditionalSystemDesc');
    }
  };

  useEffect(() => {
    const handleAuthCallback = async () => {
      // 检查认证回调参数
      const authSuccess = searchParams.get('auth_success');
      const authError = searchParams.get('auth_error');

      if (authSuccess === 'true') {
        console.log('检测到认证成功回调');

        toast.success(t('loginSuccessWelcome'), {
          duration: 3000,
          icon: <CheckCircle className="h-4 w-4" />,
        });

        // 清理URL参数
        setSearchParams({}, { replace: true });
      } else if (authError) {
        console.log('检测到认证错误回调:', authError);
        toast.error(`${t('loginFailedIndex')}: ${decodeURIComponent(authError)}`, {
          duration: 5000,
          icon: <XCircle className="h-4 w-4" />,
        });

        // 清理URL参数
        setSearchParams({}, { replace: true });
      }
    };

    handleAuthCallback();
  }, [searchParams, setSearchParams]);

  return (
    <div className="min-h-screen">
      <Header />
      
      {/* Hero Section - 统一重构版本 */}
      <section className={getHeroSectionStyles()} style={getHeroBackgroundStyle()}>
        {/* 主题差异化背景效果 */}
        {currentTheme === 'modern' && <ModernBackgroundEffects />}
        {currentTheme === 'cute' && <CuteBackgroundEffects />}

        <div className="max-w-6xl mx-auto text-center space-y-16 w-full relative z-10">
          {/* 主题特色标识 */}
          <div className="space-y-8 flex flex-col items-center">
            {currentTheme === 'cute' && (
              <div className="flex justify-center space-x-6 mb-10">
                <Star className="w-12 h-12 text-yellow-400 animate-pulse" />
                <Heart className="w-16 h-16 text-pink-400 animate-bounce" />
                <Star className="w-12 h-12 text-yellow-400 animate-pulse" />
              </div>
            )}

            {/* 主标题 */}
            <h1 className={getMainTitleStyles()} style={currentTheme === 'modern' ? {} : {textShadow: '3px 3px 6px rgba(0,0,0,0.15)'}}>
              {getThemeTitle()}
            </h1>

            {/* 描述内容 */}
            {currentTheme === 'modern' ? (
              <p className={getDescriptionStyles()} style={{textShadow: '1px 1px 2px rgba(0,0,0,0.5)'}}>
                {getThemeDescription()}
              </p>
            ) : (
              <div className={getDescriptionStyles()}>
                <p className="text-xl md:text-2xl lg:text-3xl text-pink-700 leading-relaxed font-medium">
                  {getThemeDescription()}
                </p>
              </div>
            )}
          </div>

          {/* 特色功能展示 */}
          <div className="flex justify-center">
            {currentTheme === 'modern' ? (
              <div className="grid md:grid-cols-2 gap-8 max-w-4xl">
                <div className="bg-white/5 backdrop-blur-md rounded-2xl p-8 border border-white/10 hover:bg-white/10 transition-all duration-300">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-yellow-500/30 to-orange-500/30 rounded-full flex items-center justify-center mr-4">
                      <BarChart3 className="w-6 h-6 text-yellow-400" />
                    </div>
                    <h3 className="text-xl font-semibold text-white">{t('intelligentAlgorithm')}</h3>
                  </div>
                  <p className="text-gray-300">{t('deepLearningAnalysis')}</p>
                </div>

                <div className="bg-white/5 backdrop-blur-md rounded-2xl p-8 border border-white/10 hover:bg-white/10 transition-all duration-300">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500/30 to-purple-500/30 rounded-full flex items-center justify-center mr-4">
                      <Database className="w-6 h-6 text-blue-400" />
                    </div>
                    <h3 className="text-xl font-semibold text-white">{t('traditionalIntegration')}</h3>
                  </div>
                  <p className="text-gray-300">{t('modernTechTradition')}</p>
                </div>
              </div>
            ) : (
              <div className="flex flex-wrap justify-center gap-8">
                <div className="bg-white/90 backdrop-blur-sm rounded-full px-10 py-6 shadow-xl border-3 border-pink-200 hover:scale-105 transition-transform duration-300">
                  <div className="flex items-center space-x-4">
                    <Heart className="w-8 h-8 text-pink-400" />
                    <span className="font-semibold text-lg text-pink-600">{t('warmCompanion')}</span>
                  </div>
                </div>
                <div className="bg-white/90 backdrop-blur-sm rounded-full px-10 py-6 shadow-xl border-3 border-orange-200 hover:scale-105 transition-transform duration-300">
                  <div className="flex items-center space-x-4">
                    <Sun className="w-8 h-8 text-orange-400" />
                    <span className="font-semibold text-lg text-orange-600">{t('sunnyHealing')}</span>
                  </div>
                </div>
                <div className="bg-white/90 backdrop-blur-sm rounded-full px-10 py-6 shadow-xl border-3 border-yellow-200 hover:scale-105 transition-transform duration-300">
                  <div className="flex items-center space-x-4">
                    <Sparkles className="w-8 h-8 text-yellow-400" />
                    <span className="font-semibold text-lg text-yellow-600">{t('magicalDivination')}</span>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* CTA按钮区域 */}
          <div className="flex justify-center">
            {currentTheme === 'modern' ? (
              <Button
                className={getCtaButtonStyles()}
                onClick={() => document.getElementById('divination-interface')?.scrollIntoView({ behavior: 'smooth' })}
              >
                <Zap className="w-5 h-5 mr-2" />
                {t('startAiAnalysis')}
              </Button>
            ) : (
              <Button
                className={getCtaButtonStyles()}
                onClick={() => document.getElementById('divination-interface')?.scrollIntoView({ behavior: 'smooth' })}
              >
                <Heart className="w-8 h-8 mr-4" />
                {t('startDivination')}
              </Button>
            )}
          </div>
        </div>
      </section>

      {/* Main Interface */}
      <div
        id="divination-interface"
        className={getMainInterfaceStyles()}
        style={getMainInterfaceBackgroundStyle()}
      >
        {/* 主题背景效果 */}
        {currentTheme === 'modern' && (
          <div className="absolute inset-0 overflow-hidden opacity-30">
            {[...Array(30)].map((_, i) => (
              <div
                key={`interface-star-${i}`}
                className="absolute bg-white rounded-full opacity-40"
                style={{
                  width: `${0.5 + Math.random() * 1.5}px`,
                  height: `${0.5 + Math.random() * 1.5}px`,
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  animation: `twinkle ${2 + Math.random() * 3}s ease-in-out infinite`,
                  animationDelay: `${Math.random() * 4}s`
                }}
              />
            ))}
          </div>
        )}

        {currentTheme === 'cute' && <CuteBackgroundEffects />}

        <div className="max-w-7xl mx-auto relative z-10">
          <div className="max-w-5xl mx-auto">
            <DivinationInterface />
          </div>
        </div>
      </div>

      {/* Features Section */}
      <section
        id="features-section"
        className={getFeaturesSectionStyles()}
        style={getFeaturesBackgroundStyle()}
      >
        {/* 主题背景效果 */}
        {currentTheme === 'modern' && (
          <div className="absolute inset-0 overflow-hidden opacity-20">
            {[...Array(20)].map((_, i) => (
              <div
                key={`features-star-${i}`}
                className="absolute bg-blue-200 rounded-full opacity-30"
                style={{
                  width: `${0.5 + Math.random() * 1}px`,
                  height: `${0.5 + Math.random() * 1}px`,
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  animation: `twinkle ${3 + Math.random() * 2}s ease-in-out infinite`,
                  animationDelay: `${Math.random() * 3}s`
                }}
              />
            ))}
          </div>
        )}

        <div className="max-w-6xl mx-auto relative z-10 flex flex-col justify-center min-h-full">
          {/* 标题区域 */}
          <ScrollReveal direction="up" delay={200}>
            <div className="text-center mb-20 md:mb-24 space-y-8">
              <h2 className={getFeaturesTitleStyles()}>
                {getFeaturesSectionTitle()}
              </h2>
              <p className={getFeaturesDescriptionStyles()}>
                {getFeaturesSectionDescription()}
              </p>
            </div>
          </ScrollReveal>

          {/* 特色功能网格 */}
          <div className={getFeatureCardsGridStyles()}>
            {/* 第一个功能卡片 - 传统占卜 */}
            <ScrollReveal direction="up" delay={400}>
              <DivinationCard
              title={currentTheme === 'modern' ? t('intelligentPlanning') :
                     currentTheme === 'cute' ? t('easyPlanning') :
                     t('traditionalPlanning')}
              className="card-primary hover-lift hover-scale"
            >
              <div className="space-y-8">
                {/* 图标区域 */}
                <div className="text-center py-8">
                   <div className={cn(
                     "w-20 h-20 mx-auto mb-4 rounded-full flex items-center justify-center transition-all duration-300 group-hover:scale-110",
                     currentTheme === 'cute' ?
                       "bg-gradient-to-br from-pink-400/30 to-orange-400/30 shadow-lg shadow-pink-200/50" :
                       "bg-gradient-to-br from-blue-500/30 to-purple-500/30 shadow-lg shadow-blue-200/50"
                   )}>
                     {currentTheme === 'modern' ?
                       <TrendingUp className="w-10 h-10 text-blue-400" /> :
                       <Heart className="w-10 h-10 text-pink-500" />}
                   </div>
                 </div>

                 {/* 描述文本 */}
                  <p className={cn(
                    "text-base text-center mb-6 leading-relaxed",
                    currentTheme === 'cute' ? 'text-pink-700 font-medium' : 'text-slate-300'
                  )}>
                    {currentTheme === 'modern' ? t('traditionalDivinationDesc') :
                     t('cuteDivinationDesc')}
                  </p>

                  {/* 特色列表 */}
                 <ul className={cn(
                   "text-sm space-y-3",
                   currentTheme === 'cute' ? 'text-pink-600' : 'text-slate-400'
                 )}>
                   <li className="flex items-center">
                     <span className={cn(
                       "w-2 h-2 rounded-full mr-3 flex-shrink-0",
                       currentTheme === 'cute' ? 'bg-pink-400' : 'bg-blue-400'
                     )}></span>
                      <span className="font-medium">
                        {currentTheme === 'modern' ? t('intelligentPlanning') : t('easyPlanning')}
                      </span>
                   </li>
                   <li className="flex items-center">
                     <span className={cn(
                       "w-2 h-2 rounded-full mr-3 flex-shrink-0",
                       currentTheme === 'cute' ? 'bg-pink-400' : 'bg-blue-400'
                     )}></span>
                     <span className="font-medium">{t('oldYin')}/{t('oldYang')}</span>
                   </li>
                   <li className="flex items-center">
                     <span className={cn(
                       "w-2 h-2 rounded-full mr-3 flex-shrink-0",
                       currentTheme === 'cute' ? 'bg-pink-400' : 'bg-blue-400'
                     )}></span>
                     <span className="font-medium">{t('world')}/{t('response')}</span>
                   </li>
                </ul>
              </div>
            </DivinationCard>
            </ScrollReveal>

            {/* 第二个功能卡片 - AI智能分析 */}
            <ScrollReveal direction="up" delay={600}>
              <DivinationCard
              title={currentTheme === 'modern' ? t('aiSmartAnalysis') :
                     currentTheme === 'cute' ? t('warmInterpretation') :
                     t('professionalAnalysisTitle')}
              glowing
              className="card-elevated hover-lift hover-glow"
            >
              <div className="space-y-8">
                {/* 图标区域 */}
                <div className="text-center py-8">
                  <div className={cn(
                    "w-20 h-20 mx-auto mb-4 rounded-full flex items-center justify-center transition-all duration-300 group-hover:scale-110",
                    currentTheme === 'cute' ?
                      "bg-gradient-to-br from-yellow-400/30 to-orange-400/30 shadow-lg shadow-yellow-200/50" :
                      "bg-gradient-to-br from-yellow-500/30 to-orange-500/30 shadow-lg shadow-yellow-200/50"
                  )}>
                    {currentTheme === 'modern' ?
                      <Zap className="w-10 h-10 text-yellow-400" /> :
                      currentTheme === 'cute' ?
                      <Sparkles className="w-10 h-10 text-yellow-500" /> :
                      <Shield className="w-10 h-10 text-yellow-400" />}
                  </div>
                </div>

                {/* 描述文本 */}
                <p className={cn(
                  "text-base text-center mb-6 leading-relaxed",
                  currentTheme === 'cute' ? 'text-orange-700 font-medium' : 'text-slate-300'
                )}>
                  {currentTheme === 'modern' ? t('modernAnalysisDesc') : t('cuteAnalysisDesc')}
                </p>

                {/* 特色列表 */}
                <ul className={cn(
                  "text-sm space-y-3",
                  currentTheme === 'cute' ? 'text-orange-600' : 'text-slate-400'
                )}>
                  <li className="flex items-center">
                    <span className={cn(
                      "w-2 h-2 rounded-full mr-3 flex-shrink-0",
                      currentTheme === 'cute' ? 'bg-yellow-400' : 'bg-yellow-400'
                    )}></span>
                    <span className="font-medium">{t('professionalDetailedReading')}</span>
                  </li>
                  <li className="flex items-center">
                    <span className={cn(
                      "w-2 h-2 rounded-full mr-3 flex-shrink-0",
                      currentTheme === 'cute' ? 'bg-yellow-400' : 'bg-yellow-400'
                    )}></span>
                    <span className="font-medium">{t('personalizedSuggestions')}</span>
                  </li>
                </ul>
              </div>
            </DivinationCard>
            </ScrollReveal>

            {/* 第三个功能卡片 - 云端管理 */}
            <ScrollReveal direction="up" delay={800}>
              <DivinationCard
              title={currentTheme === 'modern' ? t('cloudManagement') : t('warmRecordsTitle')}
              className="card-primary hover-lift hover-scale"
            >
              <div className="space-y-8">
                {/* 图标区域 */}
                <div className="text-center py-8">
                  <div className={cn(
                    "w-20 h-20 mx-auto mb-4 rounded-full flex items-center justify-center transition-all duration-300 group-hover:scale-110",
                    currentTheme === 'cute' ?
                      "bg-gradient-to-br from-purple-400/30 to-pink-400/30 shadow-lg shadow-purple-200/50" :
                      "bg-gradient-to-br from-purple-500/30 to-blue-500/30 shadow-lg shadow-purple-200/50"
                  )}>
                    <Shield className="w-10 h-10 text-purple-400" />
                  </div>
                </div>

                {/* 描述文本 */}
                <p className={cn(
                  "text-base text-center mb-6 leading-relaxed",
                  currentTheme === 'cute' ? 'text-purple-700 font-medium' : 'text-slate-300'
                )}>
                  {currentTheme === 'modern' ? t('modernRecordDesc') : t('cuteRecordDesc')}
                </p>

                {/* 特色列表 */}
                <ul className={cn(
                  "text-sm space-y-3",
                  currentTheme === 'cute' ? 'text-purple-600' : 'text-slate-400'
                )}>
                  <li className="flex items-center">
                    <span className={cn(
                      "w-2 h-2 rounded-full mr-3 flex-shrink-0",
                      currentTheme === 'cute' ? 'bg-purple-400' : 'bg-purple-400'
                    )}></span>
                    <span className="font-medium">
                      {currentTheme === 'modern' ? t('cloudHistoryRecords') : t('warmRecordSaving')}
                    </span>
                  </li>
                  <li className="flex items-center">
                    <span className={cn(
                      "w-2 h-2 rounded-full mr-3 flex-shrink-0",
                      currentTheme === 'cute' ? 'bg-purple-400' : 'bg-purple-400'
                    )}></span>
                    <span className="font-medium">
                      {currentTheme === 'modern' ? t('intelligentClassification') : t('shareCollection')}
                    </span>
                  </li>
                  <li className="flex items-center">
                    <span className={cn(
                      "w-2 h-2 rounded-full mr-3 flex-shrink-0",
                      currentTheme === 'cute' ? 'bg-purple-400' : 'bg-purple-400'
                    )}></span>
                    <span className="font-medium">{t('oneKeySharing')}</span>
                  </li>
                </ul>
              </div>
            </DivinationCard>
            </ScrollReveal>
          </div>
        </div>
      </section>
      
      <Footer />
    </div>
  );
};

export default Index;
