import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Header } from '@/components/Header';
import { Footer } from '@/components/Footer';
import { PaymentButton } from '@/components/PaymentButton';
import { useThemeStore } from '@/stores/themeStore';
import { useAuthStore } from '@/stores/authStore';
import { useTranslation } from '@/hooks/useTranslation';
import { useToast } from '@/components/ui/use-toast';
import { 
  Check, 
  Zap, 
  Crown, 
  Sparkles, 
  Star,
  Heart,
  Scroll
} from 'lucide-react';

interface PricingPlan {
  id: string;
  name: string;
  price: string;
  originalPrice?: string;
  credits: number;
  description: string;
  features: string[];
  popular?: boolean;
  icon: React.ComponentType<any>;
  priceId?: string;
}

export const Pricing: React.FC = () => {
  const { currentTheme } = useThemeStore();
  const { user } = useAuthStore();
  const { toast } = useToast();
  const { t } = useTranslation();

  // Get theme specific icon
  const getThemeIcon = () => {
    switch (currentTheme) {
      case 'modern': return Zap;
      case 'cute': return Heart;
      default: return Zap;
    }
  };

  const ThemeIcon = getThemeIcon();

  const pricingPlans: PricingPlan[] = [
    {
      id: 'starter',
      name: t('starterPlan'),
      price: '$4.99',
      credits: 3,
      description: currentTheme === 'modern' ? t('starterDescription') : 
                   currentTheme === 'cute' ? t('starterDescriptionCute') : 
                   t('starterDescriptionClassic'),
      features: [
        t('aiDivination'),
        t('historicalRecords'),
        t('detailedAnalysis')
      ],
      icon: Star,
      priceId: 'price_1Rpcw0QwXJYHa0vYRkG7Q14C'
    },
    {
      id: 'popular',
      name: t('popularPlan'),
      price: '$9.99',
      originalPrice: '$12.99',
      credits: 10,
      description: currentTheme === 'modern' ? t('popularDescription') : 
                   currentTheme === 'cute' ? t('popularDescriptionCute') : 
                   t('popularDescriptionClassic'),
      features: [
        t('aiDivination'),
        t('historicalRecords'),
        t('detailedAnalysis'),
        t('prioritySupport')
      ],
      popular: true,
      icon: Sparkles,
      priceId: 'price_1Rpcw9QwXJYHa0vYvAJFSnJ5'
    },
    {
      id: 'premium',
      name: t('premiumPlan'),
      price: '$19.99',
      originalPrice: '$29.99',
      credits: 25,
      description: currentTheme === 'modern' ? t('premiumDescription') : 
                   currentTheme === 'cute' ? t('premiumDescriptionCute') : 
                   t('premiumDescriptionClassic'),
      features: [
        t('aiDivination'),
        t('historicalRecords'),
        t('detailedAnalysis'),
        t('prioritySupport'),
        t('advancedFeatures'),
        t('customReports')
      ],
      icon: Crown,
      priceId: 'price_1RpcwGQwXJYHa0vYMCATht7Q'
    }
  ];

  const handleCustomPayment = (plan: PricingPlan) => {
    if (!user) {
      toast({
        title: t('loginRequired'),
        description: t('loginRequiredDesc'),
        variant: "destructive",
      });
      return;
    }

    // Extensible payment logic for different plans
    toast({
      title: t('featureInDevelopment'),
      description: t('paymentComingSoon'),
    });
  };

  return (
    <div className="min-h-screen">
      <Header />

      {/* Hero Section */}
      <section className={`py-20 px-6 ${
        currentTheme === 'cute' 
          ? 'bg-gradient-to-br from-pink-50 via-pink-100/30 to-purple-50' 
          : 'bg-gradient-to-br from-background via-muted/20 to-background'
      }`}>
        <div className="max-w-4xl mx-auto text-center space-y-6">
          <div className="space-y-4">
            <div className={`w-20 h-20 rounded-full flex items-center justify-center mx-auto ${
              currentTheme === 'cute' 
                ? 'bg-gradient-to-br from-pink-400 to-purple-400' 
                : 'bg-gradient-to-br from-divination-primary to-divination-accent'
            }`}>
              <ThemeIcon className="h-10 w-10 text-white" />
            </div>
            <h1 className="text-5xl md:text-6xl font-bold gradient-text leading-tight">
              {t('pricingTitle')}
            </h1>
            <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto">
              {t('pricingSubtitle')}
            </p>
          </div>

          <div className="flex flex-wrap justify-center gap-4">
            <Badge variant="secondary" className="px-6 py-3 text-base">
              <ThemeIcon className="w-5 h-5 mr-2" />
              {currentTheme === 'modern' ? t('modernAIService') :
               t('cuteWarmService')}
            </Badge>
            <Badge variant="outline" className="px-6 py-3 text-base">
              <Check className="w-5 h-5 mr-2" />
              {currentTheme === 'modern' ? t('modernAccuratePrediction') :
               t('cuteCarefulInterpretation')}
            </Badge>
          </div>
        </div>
      </section>

      {/* Pricing Plans */}
      <section className="py-20 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold gradient-text mb-4">
              {t('pricingTitle')}
            </h2>
            <p className="text-lg text-muted-foreground">
              {t('pricingSubtitle')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {pricingPlans.map((plan) => {
              const IconComponent = plan.icon;
              return (
                <Card
                  key={plan.id}
                  className={`relative overflow-hidden ${
                    plan.popular
                      ? 'card-elevated hover-glow hover-scale'
                      : 'card-primary hover-lift hover-scale'
                  }`}
                >
                  {plan.popular && (
                    <div className={`absolute top-4 right-4 ${
                      currentTheme === 'cute' 
                        ? 'bg-gradient-to-r from-pink-400 to-purple-400' 
                        : 'bg-gradient-to-r from-divination-primary to-divination-accent'
                    } text-white px-3 py-1 rounded-full text-sm font-medium`}>
                      {t('mostPopular')}
                    </div>
                  )}

                  <CardHeader className="text-center pb-4">
                    <div className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 ${
                      plan.popular 
                        ? currentTheme === 'cute' 
                          ? 'bg-gradient-to-br from-pink-400 to-purple-400' 
                          : 'bg-gradient-to-br from-divination-primary to-divination-accent'
                        : currentTheme === 'cute' 
                          ? 'bg-gradient-to-br from-pink-300 to-purple-300' 
                          : 'bg-gradient-to-br from-divination-secondary to-divination-accent'
                    }`}>
                      <IconComponent className="h-8 w-8 text-white" />
                    </div>
                    
                    <CardTitle className="text-2xl font-bold gradient-text mb-2">
                      {plan.name}
                    </CardTitle>
                    
                    <div className="flex items-center justify-center space-x-2 mb-2">
                      {plan.originalPrice && (
                        <span className="text-lg text-muted-foreground line-through">
                          {plan.originalPrice}
                        </span>
                      )}
                      <span className="text-4xl font-bold gradient-text">
                        {plan.price}
                      </span>
                    </div>
                    
                    <p className="text-sm text-muted-foreground mb-4">
                      {plan.description}
                    </p>
                    
                    <Badge variant={plan.popular ? "default" : "secondary"} className="text-sm">
                      {plan.credits} {t('analyses')}
                    </Badge>
                  </CardHeader>

                  <CardContent className="space-y-6">
                    <ul className="space-y-3">
                      {plan.features.map((feature, index) => (
                        <li key={index} className="flex items-center space-x-3">
                          <Check className={`h-5 w-5 flex-shrink-0 ${
                            plan.popular 
                              ? 'text-divination-primary' 
                              : 'text-muted-foreground'
                          }`} />
                          <span className="text-sm">{feature}</span>
                        </li>
                      ))}
                    </ul>

                    <PaymentButton 
                      onPaymentSuccess={() => {}} 
                      priceId={plan.priceId}
                      credits={plan.credits}
                      planName={plan.name}
                    />
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className={`py-20 px-6 ${
        currentTheme === 'cute' 
          ? 'bg-gradient-to-br from-pink-50/50 to-purple-50/50' 
          : 'bg-muted/20'
      }`}>
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold gradient-text mb-4">
              {currentTheme === 'modern' ? t('faqTitle') : 
               currentTheme === 'cute' ? t('faqTitleCute') : 
               t('faqTitleClassic')}
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <Card className="card-primary hover-lift">
              <CardContent className="p-6">
                <h3 className="font-semibold mb-2">
                  {currentTheme === 'modern' ? t('howToUseAnalysis') : 
                   currentTheme === 'cute' ? t('howToUseAnalysisCute') : 
                   t('howToUseAnalysisClassic')}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {currentTheme === 'modern' ? t('howToUseAnalysisAnswer') : 
                   currentTheme === 'cute' ? t('howToUseAnalysisAnswerCute') : 
                   t('howToUseAnalysisAnswerClassic')}
                </p>
              </CardContent>
            </Card>

            <Card className="card-primary hover-lift">
              <CardContent className="p-6">
                <h3 className="font-semibold mb-2">
                  {currentTheme === 'modern' ? t('analysisExpiry') : 
                   currentTheme === 'cute' ? t('analysisExpiryCute') : 
                   t('analysisExpiryClassic')}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {currentTheme === 'modern' ? t('analysisExpiryAnswer') : 
                   currentTheme === 'cute' ? t('analysisExpiryAnswerCute') : 
                   t('analysisExpiryAnswerClassic')}
                </p>
              </CardContent>
            </Card>

            <Card className="card-primary hover-lift">
              <CardContent className="p-6">
                <h3 className="font-semibold mb-2">
                  {currentTheme === 'modern' ? t('paymentMethods') : 
                   currentTheme === 'cute' ? t('paymentMethodsCute') : 
                   t('paymentMethodsClassic')}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {currentTheme === 'modern' ? t('paymentMethodsAnswer') : 
                   currentTheme === 'cute' ? t('paymentMethodsAnswerCute') : 
                   t('paymentMethodsAnswerClassic')}
                </p>
              </CardContent>
            </Card>

            <Card className="card-primary hover-lift">
              <CardContent className="p-6">
                <h3 className="font-semibold mb-2">
                  {currentTheme === 'modern' ? t('refundPolicy') : 
                   currentTheme === 'cute' ? t('refundPolicy') : 
                   t('refundPolicyClassic')}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {currentTheme === 'modern' ? t('refundPolicyAnswer') : 
                   currentTheme === 'cute' ? t('refundPolicyAnswerCute') : 
                   t('refundPolicyAnswerClassic')}
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};