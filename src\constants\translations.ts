export const translations = {
  zh: {
    // Header
    appName: 'FortuneTeller.Today',
    aiAnalysis: 'AI智能分析',
    pricing: '六爻价格',
    loading: '加载中...',
    profile: '个人页面',
    profilePage: '个人主页',
    login: '登录',
    logout: '退出登录',
    
    // Footer
    coreFeatures: '核心功能',
    learningResources: '学习资源',
    contactUs: '联系我们',
    copyright: '© 2024 FortuneTeller.Today. 保留所有权利.',
    tagline: '每日运势与智慧的指引',
    
    // Pricing
    pricingTitle: '选择最适合您的套餐',
    pricingSubtitle: '开始您的占卜之旅，体验AI智能分析的神奇魅力',
    starterPlan: '入门套餐',
    popularPlan: '高级套餐',
    premiumPlan: '专业套餐',
    mostPopular: '最受欢迎',
    analyses: '次分析',
    selectPlan: '选择套餐',
    selectThis: '选择这个♡',
    selectPackage: '选择此包',
    
    // Features
    aiDivination: 'AI智能占卜',
    historicalRecords: '历史记录保存',
    detailedAnalysis: '详细分析报告',
    prioritySupport: '优先客服支持',
    advancedFeatures: '高级功能解锁',
    customReports: '定制化报告',
    
    // Auth
    emailPlaceholder: '请输入邮箱',
    passwordPlaceholderSimple: '请输入密码',
    signIn: '登录',
    signUp: '注册',
    signInTitle: '登录账户',
    signUpTitle: '创建账户',
    
    // Profile
    profileTitle: '个人资料',
    email: '邮箱',
    credits: '剩余次数',
    
    // Common
    save: '保存',
    cancel: '取消',
    confirm: '确认',
    delete: '删除',
    edit: '编辑',
    close: '关闭',
    
    // Auth Modal
    loginSignup: '登录 / 注册',
    loginTab: '登录',
    signupTab: '注册',
    emailLabel: '邮箱',
    passwordLabel: '密码',
    confirmPasswordLabel: '确认密码',
    loginButton: '登录',
    signupButton: '注册',
    orDivider: '或',
    googleLogin: '使用 Google 登录',
    fillEmailPassword: '请填写邮箱和密码',
    loginSuccess: '登录成功',
    loginFailed: '登录失败',
    fillAllFields: '请填写所有字段',
    passwordMismatch: '密码不匹配',
    passwordMinLength: '密码至少需要6个字符',
    signupSuccess: '注册成功，请检查邮箱验证链接',
    signupFailed: '注册失败',
    googleLoginFailed: 'Google登录失败',
    passwordPlaceholder: '请输入密码（至少6个字符）',
    confirmPasswordPlaceholder: '请再次输入密码',
    
    // Auto Coin Toss
    tossRound: '次摇卦',
    tossHistory: '摇卦历史',
    tossInProgress: '摇卦中...',
    tossCompleted: '摇卦完成',
    tossAction: '摇卦',

    heads: '字',
    tails: '背',
    yaoNumber: '爻',
    
    // Divination Interface
    pleaseInputQuestion: '请输入问题',
    inputQuestionFirst: '请先输入您要占卜的问题',
    hexagramGenerated: '排盘成功',
    hexagramGeneratedDesc: '已为您生成卦象，可进行分析',
    pleaseLogin: '请先登录',
    analysisRequiresLogin: '详细深度分析需要登录后使用',
    analysisSubmitted: '分析请求已提交',
    analysisInProgress: '正在为您进行AI深度分析，请前往个人主页查看进度',
    
    // Main interface
    enterQuestion: '请输入您的问题',
    questionPlaceholder: '请详细描述您想要占卜的问题，如：事业发展、感情状况、财运等...',
    autoTossLong: '自动摇卦排盘',
    manualTossLong: '手动摇卦排盘',
    tossResult: '起卦结果',
    gregorianDate: '公历：',
    lunarDate: '农历：',
    solarTerm: '节气：',
    stems: '干支：',
    voidness: '空亡：',
    spirits: '神煞：',
    tossProcess: '摇卦过程',
    tossResultSummary: '起卦结果：',
    aiAnalysisLong: 'AI智能分析',
    detailedAnalysisLong: '详细深度分析',
    paid: '付费',
    saveRecord: '保存摇卦记录',
    saving: '保存中...',
    viewHistory: '查看历史记录',
    loginToSave: '请先登录后保存摇卦记录',
    aiAnalyzing: 'AI正在分析卦象...',
    oldYin: '老阴',
    oldYang: '老阳',
    youngYang: '少阳',
    youngYin: '少阴',
    
    // Index page
    modernTitle: 'AI智能占卜系统',
    cuteTitle: '小占卜屋 ♡',
    modernDesc: '基于深度学习的智能占卜平台，融合传统易学与现代AI技术',
    cuteDesc: '温馨可爱的占卜小屋，让古老的智慧变得亲切友好，陪伴你的每一次求问',
    loginSuccessWelcome: '登录成功！欢迎使用FortuneTeller.Today',
    loginFailedIndex: '登录失败',
    aiDrivenAnalysis: 'AI驱动分析',
    intelligentAlgorithm: '智能算法',
    deepLearningDesc: '深度学习驱动的占卜分析',
    traditionCombined: '传统结合',
    modernWisdomDesc: '现代科技与古老智慧完美融合',
    startAiAnalysis: '启动AI分析',
    aiTechnology: 'AI智能技术',
    learnMore: '了解更多',
    deepLearningAnalysis: '深度学习驱动的占卜分析',
    modernTechTradition: '现代科技与传统智慧完美融合',
    traditionalIntegration: '传统融合',
    warmCompanion: '温馨陪伴',
    sunnyHealing: '阳光治愈',
    magicalDivination: '神奇占卜',
    startDivination: '开始占卜 ♡',
    traditionalCulture: '传统文化',
    inheritWisdom: '承继千年占卜智慧',
    authorityInterpretation: '权威解读',
    professionalAnalysis: '专业占卜理论分析',
    deepGuidance: '深度指引',
    lifeWisdomDesc: '人生智慧指点迷津',
    startCoinToss: '开始摇卦',
    coreFeaturesHighlight: '核心功能特色',
    professionalAnalysisDesc: '专业排盘 · 智能分析 · 深度解读',
    intelligentPlanning: '智能排盘',
    easyPlanning: '轻松排盘',
    traditionalPlanning: '传统排盘',
    
    // Manual Coin Input
    manualDateSetting: '手动设置日期时间',
    selectDateTime: '选择用于计算干支的日期时间：',
    selectDateTimePlaceholder: '选择日期和时间',
    useCurrentTime: '将使用当前时间计算干支',
    yaoLineNumber: '第 {number} 爻',
    clickCoinResults: '请点击三个硬币的结果',
    coinNumber: '硬币 {number}',
    coinWord: '字',
    coinBack: '背',
    selectedCoins: '已选择: {coins}',
    completedLines: '已完成 {count}/6 爻',
    tossRecord: '摇卦记录',
    lineNumber: '第{number}爻',
    
    // Profile Simple
    dataCenter: '数据中心',
    myDivinationDiary: '我的占卜日记',
    personalCollection: '个人典藏',
    smartStats: '智能统计',
    warmRecords: '温馨记录',
    collectionManagement: '典藏管理',
    dataAnalysis: '数据分析',
    timeline: '时光轴',
    historicalReview: '历史回顾',
    totalDivinations: '总摇卦次数',
    remainingAnalyses: '剩余分析次数',
    divinationDataRecords: '占卜数据记录',
    myDivinationRecord: '我的占卜日记',
    hexagramChronicles: '摇卦史册',
    noDataRecords: '暂无数据记录',
    noDivinationRecords: '还没有占卜记录呢',
    noHexagramChronicles: '尚无摇卦史册',
    startFirstAnalysis: '开始您的第一次智能占卜分析',
    startFirstDivination: '去首页开始你的第一次温馨占卜吧！',
    startIChingJourney: '前往首页开启占卜之旅',
    startAnalysis: '开始分析',
    startDivinationHeart: '开始占卜♡',
    startHexagram: '开始摇卦',
    queueing: '排队中',
    analyzing: '分析中',
    completed: '已完成',
    failed: '失败',
    question: '问题',
    fetchCreditsError: '获取余额失败',
    cannotFetchAnalysisCount: '无法获取您的分析次数',
    pleaseLoginFirst: '请先登录',
    backToHome: '返回首页',
    reload: '重新加载',
    error: '错误',
    
    // HexagramDisplay component
    originalHexagram: '本卦',
    changedHexagram: '变卦',
    sixSpirits: '六神',
    sixRelations: '六亲',
    hexagramSymbol: '爻象',
    worldResponse: '世应',
    hiddenSpirit: '伏神',
    world: '世',
    response: '应',

    // Six Spirits translations
    qingLong: '青龙',
    zhuQue: '朱雀',
    gouChen: '勾陈',
    tengShe: '螣蛇',
    baiHu: '白虎',
    xuanWu: '玄武',

    // Six Relations translations
    guanGui: '官鬼',
    qiCai: '妻财',
    xiongDi: '兄弟',
    fuMu: '父母',
    ziSun: '子孙',

    // Heavenly Stems (天干) - Chinese
    stemJia: '甲',
    stemYi: '乙',
    stemBing: '丙',
    stemDing: '丁',
    stemWu: '戊',
    stemJi: '己',
    stemGeng: '庚',
    stemXin: '辛',
    stemRen: '壬',
    stemGui: '癸',

    // Earthly Branches (地支) - Chinese
    branchZi: '子',
    branchChou: '丑',
    branchYin: '寅',
    branchMao: '卯',
    branchChen: '辰',
    branchSi: '巳',
    branchWu: '午',
    branchWei: '未',
    branchShen: '申',
    branchYou: '酉',
    branchXu: '戌',
    branchHai: '亥',

    // Five Elements (五行) - Chinese
    elementJin: '金',
    elementMu: '木',
    elementShui: '水',
    elementHuo: '火',
    elementTu: '土',

    // SavedRecordDisplay component
    divinationQuestion: '占卜问题',
    divinationResult: '起卦结果',
    gregorianCalendar: '公历',
    lunarCalendar: '农历',
    solarTermDisplay: '节气',
    heavenlyStems: '干支',
    voicelessDisplay: '空亡',
    spiritsDisplay: '神煞',
    coinTossProcess: '摇卦过程',
    lineNumberTemplate: '第{number}爻',
    headsDisplay: '字',
    tailsDisplay: '背',
    oldYinDisplay: '老阴',
    oldYangDisplay: '老阳',
    youngYinDisplay: '少阴',
    youngYangDisplay: '少阳',
    aiAnalysisResult: 'AI分析结果',
    analysisPending: '分析请求已提交，正在排队等待处理...',
    analysisInProgressMsg: '正在进行AI深度分析，请稍候...',
    analysisFailed: '分析失败，请稍后重试或联系客服',
    noAnalysisResult: '暂无分析结果',
    recordInfo: '记录信息',
    saveTime: '保存时间',
    recordId: '记录ID',
    cannotGetHexagramData: '无法获取卦象数据',

    // SavedRecordsPanel component
    recentRecords: '最近记录',
    loginToViewHistory: '请登录查看历史记录',
    noDivinationRecordsPanel: '暂无占卜记录',

    // UserCredits component
    analysisCredits: '分析次数',
    loginToViewCredits: '请登录查看您的分析次数',
    loadingCredits: '加载中...',
    remainingCredits: '剩余次数',
    totalPurchased: '总购买次数',
    lastPurchase: '最后购买',
    noCreditsMessage: '您当前没有分析次数，请购买后使用专业分析功能',
    fetchCreditsFailed: '获取余额失败',
    cannotGetCredits: '无法获取您的分析次数',
    paymentSuccessMsg: '支付成功',
    creditsUpdated: '您的分析次数已更新',
    timesUnit: '次',
    recordsHistory: '记录历史',
    goToHomepage: '前往首页',
    designStyle: '设计风格',
    modernTheme: '现代科技',
    cuteTheme: '温馨可爱',
    language: '语言',
    aiSmartAnalysis: 'AI智能分析',
    warmInterpretation: '贴心解读',
    professionalAnalysisTitle: '专业分析',
    cloudManagement: '云端管理',
    warmRecordsTitle: '温馨记录',
    archiveManagement: '史册管理',
    
    // FAQ Section
    faqTitle: '常见问题',
    faqTitleCute: '小问题解答',
    
    // FAQ Questions
    howToUseAnalysis: '分析次数如何使用？',
    howToUseAnalysisCute: '分析次数怎么用呀？',
    howToUseAnalysisClassic: '分析次数使用方式？',
    howToUseAnalysisAnswer: '每次进行专业分析会消耗1次分析次数，购买后立即到账。',
    howToUseAnalysisAnswerCute: '每次使用专业分析功能就会用掉1次哦，买完马上就能用啦！',
    howToUseAnalysisAnswerClassic: '每次专业解析消耗一次，购买后即刻生效。',
    
    analysisExpiry: '分析次数有有效期吗？',
    analysisExpiryCute: '分析次数会过期吗？',
    analysisExpiryClassic: '分析次数是否有期限？',
    analysisExpiryAnswer: '分析次数永久有效，不会过期，您可以随时使用。',
    analysisExpiryAnswerCute: '不会过期哦！买了就是你的，什么时候想用都可以～',
    analysisExpiryAnswerClassic: '永久有效，无时限约束，随时可用。',
    
    paymentMethods: '支持哪些支付方式？',
    paymentMethodsCute: '怎么付款呢？',
    paymentMethodsClassic: '支付方式有哪些？',
    paymentMethodsAnswer: '支持信用卡、借记卡等多种支付方式，安全便捷。',
    paymentMethodsAnswerCute: '支持信用卡和借记卡付款，安全又方便！',
    paymentMethodsAnswerClassic: '支持多种卡类支付，安全可靠。',
    
    refundPolicy: '可以退款吗？',
    refundPolicyClassic: '是否支持退款？',
    refundPolicyAnswer: '未使用的分析次数支持7天内退款，已使用部分不可退款。',
    refundPolicyAnswerCute: '没用过的分析次数7天内可以退款哦，用过的就不能退啦。',
    refundPolicyAnswerClassic: '未使用次数7日内可退，已使用部分不予退还。',
    
    // Pricing page specific
    starterDescription: '适合初次体验',
    starterDescriptionCute: '小可爱们的入门选择',
    popularDescription: '最受欢迎的选择',
    popularDescriptionCute: '大家都爱的超值套餐',
    premiumDescription: '深度分析用户首选',
    premiumDescriptionCute: '超值大礼包，满满都是爱',
    loginRequired: '需要登录',
    loginRequiredDesc: '请先登录再购买分析包',
    featureInDevelopment: '功能开发中',
    paymentComingSoon: '支付功能即将上线',
    modernAIService: 'AI智能分析',
    cuteWarmService: '温馨贴心服务',
    modernAccuratePrediction: '精准预测',
    cuteCarefulInterpretation: '用心解读',
    
    // Features section titles and descriptions
    intelligentDivinationSystem: 'AI智能占卜系统',
    cuteDivinationFeatures: '温馨占卜功能',
    modernSystemDesc: '基于深度学习的智能占卜平台，融合传统易学与现代AI技术，为您提供专业准确的卦象分析',
    cuteSystemDesc: '温馨可爱的占卜小屋，让古老的智慧变得亲切友好，用最温暖的方式陪伴您的每一次求问',

    // Features descriptions
    traditionalDivinationDesc: '传统摇卦方式，精准生成本卦变卦，完整保留占卜传统',
    cuteDivinationDesc: '简单几步就能开始占卜，温馨界面让占卜变得亲切可爱',
    modernAnalysisDesc: '结合DeepSeek大模型，提供深度专业的卦象分析',
    cuteAnalysisDesc: '用温馨的语言为你解读卦象，让古老智慧变得易懂',
    modernRecordDesc: '完整记录占卜历史，支持云端同步，随时随地查看',
    cuteRecordDesc: '贴心保存每次占卜，就像你的专属占卜日记本',
    
    // Feature list items
    hexagramPlanning: '六爻排盘',
    aiIntelligentAnalysis: 'AI智能分析',
    historyRecords: '历史记录',
    shareCollection: '分享收藏',
    hexagramBasics: '六爻基础',
    hexagramInterpretation: '卦象解读',
    caseAnalysis: '案例分析',
    frequentQuestions: '常见问题',
    customerSupport: '客服支持',
    feedbackSuggestions: '意见反馈',
    businessCooperation: '商务合作',
    privacyPolicy: '隐私政策',
    
    // More feature details
    cloudHistoryRecords: '云端历史记录',
    warmRecordSaving: '温馨记录保存',
    completeHistoryArchive: '完整历史存档',
    professionalDetailedReading: '专业详细解读',
    personalizedSuggestions: '个性化建议',
    intelligentClassification: '智能分类管理',
    oneKeySharing: '一键分享结果',
  },
  en: {
    // Header
    appName: 'FortuneTeller.Today',
    aiAnalysis: 'AI Analysis',
    pricing: 'Pricing',
    loading: 'Loading...',
    profile: 'Profile',
    profilePage: 'Profile',
    login: 'Login',
    logout: 'Logout',
    
    // Footer
    coreFeatures: 'Core Features',
    learningResources: 'Learning Resources',
    contactUs: 'Contact Us',
    copyright: '© 2024 FortuneTeller.Today. All rights reserved.',
    tagline: 'Your Daily Guide to Fortune & Wisdom',
    
    // Pricing
    pricingTitle: 'Choose Your Perfect Plan',
    pricingSubtitle: 'Start your divination journey and experience the magic of AI analysis',
    starterPlan: 'Starter',
    popularPlan: 'Popular',
    premiumPlan: 'Premium',
    mostPopular: 'Most Popular',
    analyses: 'analyses',
    selectPlan: 'Select Plan',
    selectThis: 'Select This♡',
    selectPackage: 'Choose This',
    
    // Features
    aiDivination: 'AI Divination',
    historicalRecords: 'Historical Records',
    detailedAnalysis: 'Detailed Analysis',
    prioritySupport: 'Priority Support',
    advancedFeatures: 'Advanced Features',
    customReports: 'Custom Reports',
    
    // Auth
    emailPlaceholder: 'Enter your email',
    passwordPlaceholderSimple: 'Enter your password',
    signIn: 'Sign In',
    signUp: 'Sign Up',
    signInTitle: 'Sign In to Your Account',
    signUpTitle: 'Create New Account',
    
    // Profile
    profileTitle: 'Profile',
    email: 'Email',
    credits: 'Credits Remaining',
    
    // Common
    save: 'Save',
    cancel: 'Cancel',
    confirm: 'Confirm',
    delete: 'Delete',
    edit: 'Edit',
    close: 'Close',
    
    // Auth Modal
    loginSignup: 'Login / Sign Up',
    loginTab: 'Login',
    signupTab: 'Sign Up',
    emailLabel: 'Email',
    passwordLabel: 'Password',
    confirmPasswordLabel: 'Confirm Password',
    loginButton: 'Login',
    signupButton: 'Sign Up',
    orDivider: 'or',
    googleLogin: 'Login with Google',
    fillEmailPassword: 'Please enter email and password',
    loginSuccess: 'Login successful',
    loginFailed: 'Login failed',
    fillAllFields: 'Please fill in all fields',
    passwordMismatch: 'Passwords do not match',
    passwordMinLength: 'Password must be at least 6 characters',
    signupSuccess: 'Registration successful, please check your email for verification link',
    signupFailed: 'Registration failed',
    googleLoginFailed: 'Google login failed',
    passwordPlaceholder: 'Enter password (at least 6 characters)',
    confirmPasswordPlaceholder: 'Enter password again',
    
    // Auto Coin Toss
    tossRound: ' coin toss',
    tossHistory: 'Toss History',
    tossInProgress: 'Tossing...',
    tossCompleted: 'Toss Complete',
    tossAction: 'Toss',

    heads: 'Heads',
    tails: 'Tails',
    yaoNumber: 'Line',
    
    // Divination Interface
    pleaseInputQuestion: 'Please input question',
    inputQuestionFirst: 'Please enter your divination question first',
    hexagramGenerated: 'Hexagram Generated',
    hexagramGeneratedDesc: 'Hexagram has been generated for analysis',
    pleaseLogin: 'Please login',
    analysisRequiresLogin: 'Detailed analysis requires login',
    analysisSubmitted: 'Analysis Request Submitted',
    analysisInProgress: 'AI analysis in progress, please check your profile page for updates',
    
    // Main interface
    enterQuestion: 'Enter Your Question',
    questionPlaceholder: 'Please describe your divination question in detail, such as: career development, relationship status, financial fortune, etc...',
    autoTossLong: 'Auto Coin Toss',
    manualTossLong: 'Manual Coin Toss',
    tossResult: 'Divination Result',
    gregorianDate: 'Gregorian: ',
    lunarDate: 'Lunar: ',
    solarTerm: 'Solar Term: ',
    stems: 'Stems: ',
    voidness: 'Void: ',
    spirits: 'Spirits: ',
    tossProcess: 'Toss Process',
    tossResultSummary: 'Toss Result: ',
    aiAnalysisLong: 'AI Analysis',
    detailedAnalysisLong: 'Detailed Analysis',
    paid: 'Paid',
    saveRecord: 'Save Record',
    saving: 'Saving...',
    viewHistory: 'View History',
    loginToSave: 'Please login to save records',
    aiAnalyzing: 'AI is analyzing hexagram...',
    oldYin: 'Old Yin',
    oldYang: 'Old Yang',
    youngYang: 'Young Yang',
    youngYin: 'Young Yin',
    
    // Index page
    modernTitle: 'AI Fortune Telling System',
    cuteTitle: 'Little Fortune House ♡',
    modernDesc: 'Intelligent fortune telling platform based on deep learning, integrating traditional divination with modern AI technology',
    cuteDesc: 'A warm and lovely fortune telling house that makes ancient wisdom friendly and approachable, accompanying every inquiry',
    loginSuccessWelcome: 'Login successful! Welcome to FortuneTeller.Today',
    loginFailedIndex: 'Login failed',
    aiDrivenAnalysis: 'AI-Driven Analysis',
    intelligentAlgorithm: 'Intelligent Algorithm',
    deepLearningDesc: 'Deep learning-powered fortune telling analysis',
    traditionCombined: 'Tradition Combined',
    modernWisdomDesc: 'Perfect fusion of modern technology and ancient wisdom',
    startAiAnalysis: 'Start AI Analysis',
    aiTechnology: 'AI Technology',
    learnMore: 'Learn More',
    deepLearningAnalysis: 'Deep learning-powered divination analysis',
    modernTechTradition: 'Perfect fusion of modern technology and ancient wisdom',
    traditionalIntegration: 'Traditional Integration',
    warmCompanion: 'Warm Companion',
    sunnyHealing: 'Sunny Healing',
    magicalDivination: 'Magical Divination',
    startDivination: 'Start Divination ♡',
    traditionalCulture: 'Traditional Culture',
    inheritWisdom: 'Inheriting thousands of years of divination wisdom',
    authorityInterpretation: 'Authoritative Interpretation',
    professionalAnalysis: 'Professional divination theoretical analysis',
    deepGuidance: 'Deep Guidance',
    lifeWisdomDesc: 'Life wisdom to guide and enlighten',
    startCoinToss: 'Start Coin Toss',
    coreFeaturesHighlight: 'Core Features',
    professionalAnalysisDesc: 'Professional Planning · Intelligent Analysis · Deep Interpretation',
    intelligentPlanning: 'Intelligent Planning',
    easyPlanning: 'Easy Planning',
    traditionalPlanning: 'Traditional Planning',
    
    // Manual Coin Input
    manualDateSetting: 'Manual Date Setting',
    selectDateTime: 'Select date and time for stem calculation:',
    selectDateTimePlaceholder: 'Select date and time',
    useCurrentTime: 'Current time will be used for calculation',
    yaoLineNumber: 'Line {number}',
    clickCoinResults: 'Click the results of three coins',
    coinNumber: 'Coin {number}',
    coinWord: 'Heads',
    coinBack: 'Tails',
    selectedCoins: 'Selected: {coins}',
    completedLines: 'Completed {count}/6 lines',
    tossRecord: 'Toss Record',
    lineNumber: 'Line {number}',
    
    // Profile Simple
    dataCenter: 'Data Center',
    myDivinationDiary: 'My Divination Diary',
    personalCollection: 'Personal Collection',
    smartStats: 'Smart Statistics',
    warmRecords: 'Warm Records',
    collectionManagement: 'Collection Management',
    dataAnalysis: 'Data Analysis',
    timeline: 'Timeline',
    historicalReview: 'Historical Review',
    totalDivinations: 'Total Divinations',
    remainingAnalyses: 'Remaining Analyses',
    divinationDataRecords: 'Divination Data Records',
    myDivinationRecord: 'My Divination Diary',
    hexagramChronicles: 'Hexagram Chronicles',
    noDataRecords: 'No Data Records',
    noDivinationRecords: 'No divination records yet',
    noHexagramChronicles: 'No hexagram chronicles',
    startFirstAnalysis: 'Start your first intelligent divination analysis',
    startFirstDivination: 'Go to homepage for your first warm divination!',
    startIChingJourney: 'Go to homepage to start your divination journey',
    startAnalysis: 'Start Analysis',
    startDivinationHeart: 'Start Divination♡',
    startHexagram: 'Start Hexagram',
    queueing: 'Queuing',
    analyzing: 'Analyzing',
    completed: 'Completed',
    failed: 'Failed',
    question: 'Question',
    fetchCreditsError: 'Failed to fetch balance',
    cannotFetchAnalysisCount: 'Cannot fetch your analysis count',
    pleaseLoginFirst: 'Please login first',
    backToHome: 'Back to Home',
    reload: 'Reload',
    error: 'Error',
    
    // HexagramDisplay component
    originalHexagram: 'Original Hexagram',
    changedHexagram: 'Changed Hexagram',
    sixSpirits: 'Six Spirits',
    sixRelations: 'Six Relations', 
    hexagramSymbol: 'Hexagram Symbol',
    worldResponse: 'World/Response',
    hiddenSpirit: 'Hidden Spirit',
    world: 'Subject Yao',
    response: 'Object Yao',

    // Six Spirits translations
    qingLong: 'Azure Dragon',
    zhuQue: 'Vermilion Phoenix',
    gouChen: 'Gouchen',
    tengShe: 'Flying Serpent',
    baiHu: 'White Tiger',
    xuanWu: 'Black Tortoise',

    // Six Relations translations
    guanGui: 'Official-Spirit',
    qiCai: 'Spouse-Wealth',
    xiongDi: 'Siblings',
    fuMu: 'Parents',
    ziSun: 'Offspring-Descendants',

    // Heavenly Stems (天干) - Pinyin
    stemJiaEn: 'Jia',
    stemYiEn: 'Yi',
    stemBingEn: 'Bing',
    stemDingEn: 'Ding',
    stemWuEn: 'Wu',
    stemJiEn: 'Ji',
    stemGengEn: 'Geng',
    stemXinEn: 'Xin',
    stemRenEn: 'Ren',
    stemGuiEn: 'Gui',

    // Earthly Branches (地支) - Pinyin
    branchZiEn: 'Zi',
    branchChouEn: 'Chou',
    branchYinEn: 'Yin',
    branchMaoEn: 'Mao',
    branchChenEn: 'Chen',
    branchSiEn: 'Si',
    branchWuEn: 'Wu',
    branchWeiEn: 'Wei',
    branchShenEn: 'Shen',
    branchYouEn: 'You',
    branchXuEn: 'Xu',
    branchHaiEn: 'Hai',

    // Five Elements (五行) - English
    elementJinEn: 'Metal',
    elementMuEn: 'Wood',
    elementShuiEn: 'Water',
    elementHuoEn: 'Fire',
    elementTuEn: 'Earth',

    // SavedRecordDisplay component
    divinationQuestion: 'Divination Question',
    divinationResult: 'Divination Result',
    gregorianCalendar: 'Gregorian',
    lunarCalendar: 'Lunar',
    solarTermDisplay: 'Solar Term',
    heavenlyStems: 'Stems',
    voicelessDisplay: 'Void',
    spiritsDisplay: 'Spirits',
    coinTossProcess: 'Coin Toss Process',
    lineNumberTemplate: 'Line {number}',
    headsDisplay: 'Heads',
    tailsDisplay: 'Tails',
    oldYinDisplay: 'Old Yin',
    oldYangDisplay: 'Old Yang',
    youngYangDisplay: 'Young Yang',
    youngYinDisplay: 'Young Yin',
    
    // Pricing page
    starterDescription: 'Perfect for first-time users',
    starterDescriptionCute: 'Perfect choice for little cuties',
    starterDescriptionClassic: 'Traditional starter choice',
    popularDescription: 'Most popular choice',
    popularDescriptionCute: 'Everyone\'s favorite value package',
    popularDescriptionClassic: 'Classic practical choice',
    premiumDescription: 'Deep analysis user\'s first choice',
    premiumDescriptionCute: 'Super value package, full of love',
    premiumDescriptionClassic: 'Deep into divination essence',
    loginRequired: 'Login Required',
    loginRequiredDesc: 'Please login before purchasing analysis package',
    featureInDevelopment: 'Feature in Development',
    paymentComingSoon: 'Payment feature coming soon',
    modernAIService: 'AI Smart Analysis',
    cuteWarmService: 'Warm & Caring Service',
    modernAccuratePrediction: 'Accurate Prediction',
    cuteCarefulInterpretation: 'Careful Interpretation',
    designStyle: 'Design Style',
    modernTheme: 'Modern Tech',
    cuteTheme: 'Cute & Warm',
    language: 'Language',
    
    // Features section titles and descriptions
    intelligentDivinationSystem: 'AI Intelligent Divination System',
    cuteDivinationFeatures: 'Warm Divination Features',
    modernSystemDesc: 'Intelligent divination platform based on deep learning, integrating traditional I-Ching with modern AI technology to provide professional and accurate hexagram analysis',
    cuteSystemDesc: 'A warm and lovely divination house that makes ancient wisdom friendly and approachable, accompanying every inquiry with the warmest care',

    // Features descriptions
    traditionalDivinationDesc: 'Traditional coin toss method, accurately generates original and changed hexagrams, preserving complete divination tradition',
    cuteDivinationDesc: 'Simple steps to start divination, warm interface makes ancient wisdom friendly and approachable',
    modernAnalysisDesc: 'Combined with DeepSeek large model, providing deep professional hexagram analysis',
    cuteAnalysisDesc: 'Interpreting hexagrams with warm language, making ancient wisdom easy to understand',
    modernRecordDesc: 'Complete recording of divination history, supports cloud synchronization, view anytime anywhere',
    cuteRecordDesc: 'Thoughtfully save every divination, like your personal divination diary',
    
    // Feature list items
    hexagramPlanning: 'Hexagram Planning',
    aiIntelligentAnalysis: 'AI Intelligent Analysis',
    historyRecords: 'History Records',
    shareCollection: 'Share & Collection',
    hexagramBasics: 'Hexagram Basics',
    hexagramInterpretation: 'Hexagram Interpretation',
    caseAnalysis: 'Case Analysis',
    frequentQuestions: 'FAQ',
    customerSupport: 'Customer Support',
    feedbackSuggestions: 'Feedback',
    businessCooperation: 'Business Cooperation',
    privacyPolicy: 'Privacy Policy',
    
    // More feature details
    cloudHistoryRecords: 'Cloud History Records',
    warmRecordSaving: 'Warm Record Saving',
    completeHistoryArchive: 'Complete History Archive',
    professionalDetailedReading: 'Professional Detailed Reading',
    personalizedSuggestions: 'Personalized Suggestions',
    intelligentClassification: 'Intelligent Classification Management',
    oneKeySharing: 'One-Key Sharing Results',
  },
};

export type TranslationKeys = keyof typeof translations.zh;