import React from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Monitor, Heart, BookOpen } from 'lucide-react';
import { useThemeStore, ThemeType } from '@/stores/themeStore';
import { useTranslation } from '@/hooks/useTranslation';
import { cn } from '@/lib/utils';

const themeIcons: Record<ThemeType, React.ComponentType<{ className?: string }>> = {
  modern: Monitor,
  cute: Heart,
};

export const ThemeSwitcher: React.FC = () => {
  const { currentTheme, setTheme } = useThemeStore();
  const { t } = useTranslation();

  const themes: ThemeType[] = ['modern', 'cute'];

  const getThemeName = (theme: ThemeType): string => {
    switch (theme) {
      case 'modern':
        return t('modernTheme');
      case 'cute':
        return t('cuteTheme');
      default:
        return theme;
    }
  };

  const getButtonStyles = () => {
    switch (currentTheme) {
      case 'modern':
        return "btn-header-nav hover-lift";
      case 'cute':
        return "btn-header-nav hover-lift";
      default:
        return "btn-header-nav";
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
        className={cn("text-sm focus-ring", getButtonStyles())}
        aria-label={`当前主题: ${currentTheme === 'modern' ? '现代科技风' : '温馨可爱风'}，点击切换主题`}
        role="button"
        tabIndex={0}
      >
          {t('designStyle')}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        {themes.map((theme) => {
          const Icon = themeIcons[theme];
          return (
            <DropdownMenuItem
              key={theme}
              onClick={() => setTheme(theme)}
              className={currentTheme === theme ? 'bg-accent' : ''}
            >
              <Icon className="h-4 w-4 mr-2" />
              {getThemeName(theme)}
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};