import React, { useState } from 'react';
import { Button } from './ui/button';
import { useToast } from './ui/use-toast';
import { useAuthStore } from '@/stores/authStore';
import { supabase } from '@/integrations/supabase/client';
import { Loader2, CreditCard } from 'lucide-react';

interface PaymentButtonProps {
  onPaymentSuccess?: () => void;
  priceId?: string;
  credits?: number;
  planName?: string;
}

export const PaymentButton: React.FC<PaymentButtonProps> = ({ 
  onPaymentSuccess, 
  priceId, 
  credits = 10, 
  planName = "分析套餐" 
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { user } = useAuthStore();

  const handlePayment = async () => {
    if (!user) {
      toast({
        title: "需要登录",
        description: "请先登录再购买分析包",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    console.log("Creating payment session...");
    console.log("Payment parameters:", { priceId, credits, planName });

    try {
      const { data, error } = await supabase.functions.invoke('create-checkout-session', {
        body: { 
          priceId,
          credits,
          planName 
        }
      });
      
      if (error) {
        console.error("Payment error:", error);
        throw new Error(error.message);
      }

      if (data?.url) {
        console.log("Redirecting to Stripe checkout:", data.url);
        // Open Stripe checkout in a new tab
        window.open(data.url, '_blank');
        
        toast({
          title: "跳转支付",
          description: "已为您打开支付页面，请完成支付",
        });
      } else {
        throw new Error("No checkout URL received");
      }
    } catch (error) {
      console.error("Error creating payment:", error);
      toast({
        title: "支付错误",
        description: error instanceof Error ? error.message : "创建支付会话失败",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      onClick={handlePayment}
      disabled={isLoading || !user}
      className="btn-primary w-full hover-lift click-scale focus-ring"
      aria-label={`购买${planName}，包含${credits}次AI分析`}
      role="button"
    >
      {isLoading ? (
        <>
          <Loader2 className="mr-2 h-3 w-3 animate-spin" />
          创建支付中...
        </>
      ) : (
        <>
          <CreditCard className="mr-2 h-3 w-3" />
          购买{credits}次分析
        </>
      )}
    </Button>
  );
};