import React from 'react';
import { Button } from '@/components/ui/button';
import { useLanguageStore, Language } from '@/stores/languageStore';
import { useThemeStore } from '@/stores/themeStore';
import { Globe } from 'lucide-react';
import { cn } from '@/lib/utils';

export const LanguageSwitcher: React.FC = () => {
  const { currentLanguage, setLanguage } = useLanguageStore();
  const { currentTheme } = useThemeStore();

  const toggleLanguage = () => {
    const newLanguage: Language = currentLanguage === 'zh' ? 'en' : 'zh';
    setLanguage(newLanguage);
  };

  const getButtonStyles = () => {
    switch (currentTheme) {
      case 'modern':
        return "btn-header-nav hover-lift";
      case 'cute':
        return "btn-header-nav hover-lift";
      default:
        return "btn-header-nav";
    }
  };

  return (
    <Button
      onClick={toggleLanguage}
      className={cn("flex items-center space-x-2 focus-ring", getButtonStyles())}
      aria-label={`当前语言: ${currentLanguage === 'zh' ? '中文' : 'English'}，点击切换语言`}
      role="button"
      tabIndex={0}
    >
      <Globe className="h-4 w-4" />
      <span className="text-sm font-medium">
        {currentLanguage === 'zh' ? '中文' : 'EN'}
      </span>
    </Button>
  );
};