import React from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { useThemeStore } from '@/stores/themeStore';
import { useTranslation } from '@/hooks/useTranslation';
import { cn } from '@/lib/utils';
import { Palette, Type, Layout, Zap, Heart, Star, Sparkles } from 'lucide-react';

export const DesignSystem: React.FC = () => {
  const { currentTheme } = useThemeStore();
  const { t } = useTranslation();

  const getPageStyles = () => {
    const baseStyles = "min-h-screen py-20 px-6";
    
    switch (currentTheme) {
      case 'modern':
        return cn(baseStyles, "bg-slate-900 text-slate-100");
      case 'cute':
        return cn(baseStyles, "bg-pink-50 text-pink-900");
      default:
        return baseStyles;
    }
  };

  const getSectionStyles = () => {
    switch (currentTheme) {
      case 'modern':
        return "space-y-12";
      case 'cute':
        return "space-y-10";
      default:
        return "space-y-8";
    }
  };

  const getTitleStyles = () => {
    switch (currentTheme) {
      case 'modern':
        return "text-4xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent mb-8";
      case 'cute':
        return "text-3xl font-bold gradient-text mb-6 flex items-center gap-3";
      default:
        return "text-4xl font-bold gradient-text mb-8";
    }
  };

  const getSectionTitleStyles = () => {
    switch (currentTheme) {
      case 'modern':
        return "text-2xl font-semibold text-blue-300 mb-6 flex items-center gap-3";
      case 'cute':
        return "text-xl font-semibold text-pink-600 mb-5 flex items-center gap-2";
      default:
        return "text-2xl font-semibold mb-6";
    }
  };

  // 色彩展示组件
  const ColorPalette = () => (
    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
      {currentTheme === 'modern' ? (
        <>
          <div className="space-y-2">
            <div className="w-full h-16 bg-blue-400 rounded-lg"></div>
            <p className="text-sm font-medium">Primary Blue</p>
            <p className="text-xs text-muted-foreground">#60A5FA</p>
          </div>
          <div className="space-y-2">
            <div className="w-full h-16 bg-purple-400 rounded-lg"></div>
            <p className="text-sm font-medium">Secondary Purple</p>
            <p className="text-xs text-muted-foreground">#A78BFA</p>
          </div>
          <div className="space-y-2">
            <div className="w-full h-16 bg-cyan-400 rounded-lg"></div>
            <p className="text-sm font-medium">Accent Cyan</p>
            <p className="text-xs text-muted-foreground">#22D3EE</p>
          </div>
          <div className="space-y-2">
            <div className="w-full h-16 bg-slate-800 rounded-lg"></div>
            <p className="text-sm font-medium">Background</p>
            <p className="text-xs text-muted-foreground">#1E293B</p>
          </div>
          <div className="space-y-2">
            <div className="w-full h-16 bg-slate-700 rounded-lg"></div>
            <p className="text-sm font-medium">Surface</p>
            <p className="text-xs text-muted-foreground">#334155</p>
          </div>
          <div className="space-y-2">
            <div className="w-full h-16 bg-slate-300 rounded-lg"></div>
            <p className="text-sm font-medium">Text</p>
            <p className="text-xs text-muted-foreground">#CBD5E1</p>
          </div>
        </>
      ) : (
        <>
          <div className="space-y-2">
            <div className="w-full h-16 bg-pink-400 rounded-2xl"></div>
            <p className="text-sm font-medium">Primary Pink</p>
            <p className="text-xs text-muted-foreground">#F472B6</p>
          </div>
          <div className="space-y-2">
            <div className="w-full h-16 bg-orange-300 rounded-2xl"></div>
            <p className="text-sm font-medium">Secondary Orange</p>
            <p className="text-xs text-muted-foreground">#FDBA74</p>
          </div>
          <div className="space-y-2">
            <div className="w-full h-16 bg-purple-300 rounded-2xl"></div>
            <p className="text-sm font-medium">Accent Purple</p>
            <p className="text-xs text-muted-foreground">#C4B5FD</p>
          </div>
          <div className="space-y-2">
            <div className="w-full h-16 bg-pink-50 rounded-2xl border-2 border-pink-200"></div>
            <p className="text-sm font-medium">Background</p>
            <p className="text-xs text-muted-foreground">#FDF2F8</p>
          </div>
          <div className="space-y-2">
            <div className="w-full h-16 bg-pink-100 rounded-2xl"></div>
            <p className="text-sm font-medium">Surface</p>
            <p className="text-xs text-muted-foreground">#FCE7F3</p>
          </div>
          <div className="space-y-2">
            <div className="w-full h-16 bg-pink-800 rounded-2xl"></div>
            <p className="text-sm font-medium">Text</p>
            <p className="text-xs text-muted-foreground">#9D174D</p>
          </div>
        </>
      )}
    </div>
  );

  // 字体展示组件
  const Typography = () => (
    <div className="space-y-6">
      <div className="space-y-4">
        <h1 className="text-5xl font-bold">标题 1 - 48px</h1>
        <h2 className="text-4xl font-bold">标题 2 - 36px</h2>
        <h3 className="text-3xl font-semibold">标题 3 - 30px</h3>
        <h4 className="text-2xl font-semibold">标题 4 - 24px</h4>
        <h5 className="text-xl font-medium">标题 5 - 20px</h5>
        <h6 className="text-lg font-medium">标题 6 - 18px</h6>
      </div>
      <div className="space-y-3">
        <p className="text-base">正文 - 16px 这是正文内容的示例，展示了基础的文字大小和行高。</p>
        <p className="text-sm">小号文字 - 14px 这是小号文字的示例，通常用于辅助信息。</p>
        <p className="text-xs">极小文字 - 12px 这是极小文字的示例，通常用于标签或说明。</p>
      </div>
    </div>
  );

  // 按钮展示组件
  const ButtonShowcase = () => (
    <div className="space-y-6">
      <div className="flex flex-wrap gap-4">
        <Button className="btn-primary">主要按钮</Button>
        <Button className="btn-secondary">次要按钮</Button>
        <Button className="btn-ghost">幽灵按钮</Button>
        <Button variant="outline">边框按钮</Button>
        <Button variant="destructive">危险按钮</Button>
      </div>
      <div className="flex flex-wrap gap-4">
        <Button size="sm" className="btn-primary">小按钮</Button>
        <Button size="default" className="btn-primary">默认按钮</Button>
        <Button size="lg" className="btn-primary">大按钮</Button>
      </div>
    </div>
  );

  // 卡片展示组件
  const CardShowcase = () => (
    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
      <Card className="card-primary">
        <CardHeader>
          <CardTitle>基础卡片</CardTitle>
        </CardHeader>
        <CardContent>
          <p>这是一个基础卡片的示例内容。</p>
        </CardContent>
      </Card>
      
      <Card className="card-elevated">
        <CardHeader>
          <CardTitle>悬浮卡片</CardTitle>
        </CardHeader>
        <CardContent>
          <p>这是一个带有悬浮效果的卡片示例。</p>
        </CardContent>
      </Card>
      
      <Card className="divination-card">
        <CardHeader>
          <CardTitle>占卜卡片</CardTitle>
        </CardHeader>
        <CardContent>
          <p>这是专门为占卜功能设计的卡片。</p>
        </CardContent>
      </Card>
    </div>
  );

  // 输入框展示组件
  const InputShowcase = () => (
    <div className="space-y-4 max-w-md">
      <Input className="input-primary" placeholder="基础输入框" />
      <Input className="input-primary" placeholder="聚焦状态输入框" />
      <Input className="input-primary" placeholder="禁用状态输入框" disabled />
    </div>
  );

  return (
    <div className={getPageStyles()}>
      <div className="max-w-7xl mx-auto">
        {/* 页面标题 */}
        <div className="text-center mb-16">
          <h1 className={getTitleStyles()}>
            {currentTheme === 'cute' && <Heart className="w-8 h-8 text-pink-500" />}
            设计系统规范
            {currentTheme === 'modern' && <Zap className="w-8 h-8 text-blue-400 ml-3 inline" />}
          </h1>
          <p className={cn(
            "text-lg max-w-2xl mx-auto",
            currentTheme === 'modern' ? "text-slate-300" : "text-pink-700"
          )}>
            完整的设计系统展示，包含色彩、字体、组件和交互规范
          </p>
        </div>

        <div className={getSectionStyles()}>
          {/* 色彩系统 */}
          <section>
            <h2 className={getSectionTitleStyles()}>
              <Palette className="w-6 h-6" />
              色彩系统
            </h2>
            <ColorPalette />
          </section>

          {/* 字体系统 */}
          <section>
            <h2 className={getSectionTitleStyles()}>
              <Type className="w-6 h-6" />
              字体排版
            </h2>
            <Typography />
          </section>

          {/* 按钮系统 */}
          <section>
            <h2 className={getSectionTitleStyles()}>
              {currentTheme === 'modern' ? <Zap className="w-6 h-6" /> : <Star className="w-6 h-6" />}
              按钮组件
            </h2>
            <ButtonShowcase />
          </section>

          {/* 卡片系统 */}
          <section>
            <h2 className={getSectionTitleStyles()}>
              <Layout className="w-6 h-6" />
              卡片组件
            </h2>
            <CardShowcase />
          </section>

          {/* 输入框系统 */}
          <section>
            <h2 className={getSectionTitleStyles()}>
              {currentTheme === 'modern' ? <Zap className="w-6 h-6" /> : <Sparkles className="w-6 h-6" />}
              输入组件
            </h2>
            <InputShowcase />
          </section>

          {/* 微交互展示 */}
          <section>
            <h2 className={getSectionTitleStyles()}>
              {currentTheme === 'modern' ? <Zap className="w-6 h-6" /> : <Star className="w-6 h-6" />}
              微交互效果
            </h2>
            <MicroInteractions />
          </section>

          {/* 间距系统 */}
          <section>
            <h2 className={getSectionTitleStyles()}>
              <Layout className="w-6 h-6" />
              间距系统
            </h2>
            <SpacingSystem />
          </section>
        </div>
      </div>
    </div>
  );
};

// 微交互展示组件
const MicroInteractions = () => {
  const { currentTheme } = useThemeStore();

  return (
    <div className="space-y-8">
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="space-y-3">
          <h4 className="font-medium">悬停上升</h4>
          <div className={cn(
            "p-4 rounded-lg border hover-lift cursor-pointer",
            currentTheme === 'modern' ? "bg-slate-800 border-slate-700" : "bg-pink-100 border-pink-200"
          )}>
            悬停我试试
          </div>
        </div>

        <div className="space-y-3">
          <h4 className="font-medium">悬停缩放</h4>
          <div className={cn(
            "p-4 rounded-lg border hover-scale cursor-pointer",
            currentTheme === 'modern' ? "bg-slate-800 border-slate-700" : "bg-pink-100 border-pink-200"
          )}>
            悬停我试试
          </div>
        </div>

        <div className="space-y-3">
          <h4 className="font-medium">悬停发光</h4>
          <div className={cn(
            "p-4 rounded-lg border hover-glow cursor-pointer",
            currentTheme === 'modern' ? "bg-slate-800 border-slate-700" : "bg-pink-100 border-pink-200"
          )}>
            悬停我试试
          </div>
        </div>

        <div className="space-y-3">
          <h4 className="font-medium">点击缩放</h4>
          <div className={cn(
            "p-4 rounded-lg border click-scale cursor-pointer",
            currentTheme === 'modern' ? "bg-slate-800 border-slate-700" : "bg-pink-100 border-pink-200"
          )}>
            点击我试试
          </div>
        </div>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        <div className="space-y-3">
          <h4 className="font-medium">加载动画</h4>
          <div className="flex gap-4">
            <div className={cn(
              "w-8 h-8 border-2 border-t-transparent rounded-full loading-spin",
              currentTheme === 'modern' ? "border-blue-400" : "border-pink-400"
            )}></div>
            <div className={cn(
              "w-4 h-4 rounded-full loading-bounce",
              currentTheme === 'modern' ? "bg-blue-400" : "bg-pink-400"
            )}></div>
          </div>
        </div>

        <div className="space-y-3">
          <h4 className="font-medium">聚焦效果</h4>
          <input
            className={cn(
              "px-4 py-2 rounded-lg border focus-ring",
              currentTheme === 'modern' ? "bg-slate-800 border-slate-700" : "bg-white border-pink-200"
            )}
            placeholder="点击聚焦查看效果"
          />
        </div>
      </div>
    </div>
  );
};

// 间距系统展示组件
const SpacingSystem = () => {
  const { currentTheme } = useThemeStore();

  const spacings = [
    { name: 'XS', value: '4px', class: 'w-1 h-1' },
    { name: 'SM', value: '8px', class: 'w-2 h-2' },
    { name: 'MD', value: '16px', class: 'w-4 h-4' },
    { name: 'LG', value: '24px', class: 'w-6 h-6' },
    { name: 'XL', value: '32px', class: 'w-8 h-8' },
    { name: '2XL', value: '48px', class: 'w-12 h-12' },
    { name: '3XL', value: '64px', class: 'w-16 h-16' },
    { name: '4XL', value: '96px', class: 'w-24 h-24' },
  ];

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-6">
      {spacings.map((spacing) => (
        <div key={spacing.name} className="text-center space-y-2">
          <div className={cn(
            spacing.class,
            "mx-auto rounded",
            currentTheme === 'modern' ? "bg-blue-400" : "bg-pink-400"
          )}></div>
          <div>
            <p className="font-medium text-sm">{spacing.name}</p>
            <p className="text-xs text-muted-foreground">{spacing.value}</p>
          </div>
        </div>
      ))}
    </div>
  );
};
